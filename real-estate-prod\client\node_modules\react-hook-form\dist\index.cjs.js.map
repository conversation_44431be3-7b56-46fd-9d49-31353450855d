{"version": 3, "file": "index.cjs.js", "sources": ["../src/utils/isCheckBoxInput.ts", "../src/utils/isDateObject.ts", "../src/utils/isNullOrUndefined.ts", "../src/utils/isObject.ts", "../src/logic/getEventValue.ts", "../src/logic/isNameInFieldArray.ts", "../src/logic/getNodeParentName.ts", "../src/utils/isWeb.ts", "../src/utils/cloneObject.ts", "../src/utils/isPlainObject.ts", "../src/utils/compact.ts", "../src/utils/isUndefined.ts", "../src/utils/get.ts", "../src/utils/isBoolean.ts", "../src/utils/isKey.ts", "../src/utils/stringToPath.ts", "../src/utils/set.ts", "../src/constants.ts", "../src/useFormContext.tsx", "../src/logic/getProxyFormState.ts", "../src/utils/isEmptyObject.ts", "../src/logic/shouldRenderFormState.ts", "../src/utils/convertToArrayPayload.ts", "../src/logic/shouldSubscribeByName.ts", "../src/useSubscribe.ts", "../src/useFormState.ts", "../src/utils/isString.ts", "../src/logic/generateWatchOutput.ts", "../src/useWatch.ts", "../src/useController.ts", "../src/controller.tsx", "../src/utils/flatten.ts", "../src/form.tsx", "../src/logic/appendErrors.ts", "../src/logic/generateId.ts", "../src/logic/getFocusFieldName.ts", "../src/logic/getValidationModes.ts", "../src/logic/isWatched.ts", "../src/logic/iterateFieldsByAction.ts", "../src/logic/updateFieldArrayRootError.ts", "../src/utils/isFileInput.ts", "../src/utils/isFunction.ts", "../src/utils/isHTMLElement.ts", "../src/utils/isMessage.ts", "../src/utils/isRadioInput.ts", "../src/utils/isRegex.ts", "../src/logic/getCheckboxValue.ts", "../src/logic/getRadioValue.ts", "../src/logic/getValidateError.ts", "../src/logic/getValueAndMessage.ts", "../src/logic/validateField.ts", "../src/utils/append.ts", "../src/utils/fillEmptyArray.ts", "../src/utils/insert.ts", "../src/utils/move.ts", "../src/utils/prepend.ts", "../src/utils/remove.ts", "../src/utils/swap.ts", "../src/utils/unset.ts", "../src/utils/update.ts", "../src/utils/createSubject.ts", "../src/utils/isPrimitive.ts", "../src/utils/deepEqual.ts", "../src/utils/isMultipleSelect.ts", "../src/utils/live.ts", "../src/utils/objectHasFunction.ts", "../src/logic/getDirtyFields.ts", "../src/logic/getFieldValueAs.ts", "../src/logic/getFieldValue.ts", "../src/logic/getResolverOptions.ts", "../src/logic/getRuleValue.ts", "../src/logic/hasPromiseValidation.ts", "../src/logic/schemaErrorLookup.ts", "../src/logic/createFormControl.ts", "../src/logic/hasValidation.ts", "../src/logic/skipValidation.ts", "../src/utils/isRadioOrCheckbox.ts", "../src/logic/unsetEmptyArray.ts", "../src/useFieldArray.ts", "../src/useForm.ts"], "sourcesContent": ["import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "import { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n  const isFileListInstance =\n    typeof FileList !== 'undefined' ? data instanceof FileList : false;\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || isFileListInstance)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "export default (val: unknown): val is undefined => val === undefined;\n", "import compact from './compact';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\n\nexport default <T>(\n  object: T,\n  path?: string | null,\n  defaultValue?: unknown,\n): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = compact(path.split(/[,[\\].]+?/)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "export default (value: string) => /^\\w*$/.test(value);\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import { FieldPath, FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (\n  object: FieldValues,\n  path: FieldPath<FieldValues>,\n  value?: unknown,\n) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n            ? []\n            : {};\n    }\n\n    if (key === '__proto__' || key === 'constructor' || key === 'prototype') {\n      return;\n    }\n\n    object[key] = newValue;\n    object = object[key];\n  }\n  return object;\n};\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TransformedValues extends FieldValues | undefined = undefined,\n>(): UseFormReturn<TFieldValues, TContext, TransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues extends FieldValues | undefined = undefined,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <TFieldValues extends FieldValues, TContext = any>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import { VALIDATION_MODE } from '../constants';\nimport {\n  Control,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & { name?: InternalFieldName },\n  _proxyFormState: K,\n  updateFormState: Control<T>['_updateFormState'],\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import React from 'react';\n\nimport { Subject } from './utils/createSubject';\n\ntype Props<T> = {\n  disabled?: boolean;\n  subject: Subject<T>;\n  next: (value: T) => void;\n};\n\nexport function useSubscribe<T>(props: Props<T>) {\n  const _props = React.useRef(props);\n  _props.current = props;\n\n  React.useEffect(() => {\n    const subscription =\n      !props.disabled &&\n      _props.current.subject &&\n      _props.current.subject.subscribe({\n        next: _props.current.next,\n      });\n\n    return () => {\n      subscription && subscription.unsubscribe();\n    };\n  }, [props.disabled]);\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport shouldRenderFormState from './logic/shouldRenderFormState';\nimport shouldSubscribeByName from './logic/shouldSubscribeByName';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState<TFieldValues extends FieldValues = FieldValues>(\n  props?: UseFormStateProps<TFieldValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _mounted = React.useRef(true);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n  const _name = React.useRef(name);\n\n  _name.current = name;\n\n  useSubscribe({\n    disabled,\n    next: (\n      value: Partial<FormState<TFieldValues>> & { name?: InternalFieldName },\n    ) =>\n      _mounted.current &&\n      shouldSubscribeByName(\n        _name.current as InternalFieldName,\n        value.name,\n        exact,\n      ) &&\n      shouldRenderFormState(\n        value,\n        _localProxyFormState.current,\n        control._updateFormState,\n      ) &&\n      updateFormState({\n        ...control._formState,\n        ...value,\n      }),\n    subject: control._subjects.state,\n  });\n\n  React.useEffect(() => {\n    _mounted.current = true;\n    _localProxyFormState.current.isValid && control._updateValid(true);\n\n    return () => {\n      _mounted.current = false;\n    };\n  }, [control]);\n\n  return React.useMemo(\n    () =>\n      getProxyFormState(\n        formState,\n        control,\n        _localProxyFormState.current,\n        false,\n      ),\n    [formState, control],\n  );\n}\n\nexport { useFormState };\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport shouldSubscribeByName from './logic/shouldSubscribeByName';\nimport cloneObject from './utils/cloneObject';\nimport {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends\n    readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _name = React.useRef(name);\n\n  _name.current = name;\n\n  useSubscribe({\n    disabled,\n    subject: control._subjects.values,\n    next: (formState: { name?: InternalFieldName; values?: FieldValues }) => {\n      if (\n        shouldSubscribeByName(\n          _name.current as InternalFieldName,\n          formState.name,\n          exact,\n        )\n      ) {\n        updateValue(\n          cloneObject(\n            generateWatchOutput(\n              _name.current as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              defaultValue,\n            ),\n          ),\n        );\n      }\n    },\n  });\n\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      defaultValue as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport set from './utils/set';\nimport { EVENTS } from './constants';\nimport {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(\n  props: UseControllerProps<TFieldValues, TName>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n    exact: true,\n  });\n\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  const fieldState = React.useMemo(\n    () =>\n      Object.defineProperties(\n        {},\n        {\n          invalid: {\n            enumerable: true,\n            get: () => !!get(formState.errors, name),\n          },\n          isDirty: {\n            enumerable: true,\n            get: () => !!get(formState.dirtyFields, name),\n          },\n          isTouched: {\n            enumerable: true,\n            get: () => !!get(formState.touchedFields, name),\n          },\n          isValidating: {\n            enumerable: true,\n            get: () => !!get(formState.validatingFields, name),\n          },\n          error: {\n            enumerable: true,\n            get: () => get(formState.errors, name),\n          },\n        },\n      ) as ControllerFieldState,\n    [formState, name],\n  );\n\n  const field = React.useMemo(\n    () => ({\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange: (event: any) =>\n        _registerProps.current.onChange({\n          target: {\n            value: getEventValue(event),\n            name: name as InternalFieldName,\n          },\n          type: EVENTS.CHANGE,\n        }),\n      onBlur: () =>\n        _registerProps.current.onBlur({\n          target: {\n            value: get(control._formValues, name),\n            name: name as InternalFieldName,\n          },\n          type: EVENTS.BLUR,\n        }),\n      ref: (elm: any) => {\n        const field = get(control._fields, name);\n\n        if (field && elm) {\n          field._f.ref = {\n            focus: () => elm.focus(),\n            select: () => elm.select(),\n            setCustomValidity: (message: string) =>\n              elm.setCustomValidity(message),\n            reportValidity: () => elm.reportValidity(),\n          };\n        }\n      },\n    }),\n    [\n      name,\n      control._formValues,\n      disabled,\n      formState.disabled,\n      value,\n      control._fields,\n    ],\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field && field._f) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    !isArrayField && control.register(name);\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    control._updateDisabledField({\n      disabled,\n      fields: control._fields,\n      name,\n    });\n  }, [disabled, name, control]);\n\n  return React.useMemo(\n    () => ({\n      field,\n      formState,\n      fieldState,\n    }),\n    [field, formState, fieldState],\n  );\n}\n", "import { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(\n  props: ControllerProps<TFieldValues, TName>,\n) => props.render(useController<TFieldValues, TName>(props));\n\nexport { Controller };\n", "import { FieldValues } from '../types';\n\nimport { isObjectType } from './isObject';\n\nexport const flatten = (obj: FieldValues) => {\n  const output: FieldValues = {};\n\n  for (const key of Object.keys(obj)) {\n    if (isObjectType(obj[key]) && obj[key] !== null) {\n      const nested = flatten(obj[key]);\n\n      for (const nestedKey of Object.keys(nested)) {\n        output[`${key}.${nestedKey}`] = nested[nestedKey];\n      }\n    } else {\n      output[key] = obj[key];\n    }\n  }\n\n  return output;\n};\n", "import React from 'react';\n\nimport { flatten } from './utils/flatten';\nimport { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  T extends FieldValues,\n  U extends FieldValues | undefined = undefined,\n>(props: FormProps<T, U>) {\n  const methods = useFormContext<T>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      const flattenFormValues = flatten(control._formValues);\n\n      for (const key in flattenFormValues) {\n        formData.append(key, flattenFormValues[key]);\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(String(action), {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default () => {\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import { VALIDATION_MODE } from '../constants';\nimport { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          return true;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          return true;\n        } else {\n          if (iterateFieldsByAction(currentField, action)) {\n            break;\n          }\n        }\n      } else if (isObject(currentField)) {\n        if (iterateFieldsByAction(currentField as FieldRefs, action)) {\n          break;\n        }\n      }\n    }\n  }\n  return;\n};\nexport default iterateFieldsByAction;\n", "import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = convertToArrayPayload(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import { FieldError, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  InternalNameSet,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  disabledFieldNames: InternalNameSet,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabledFieldNames.has(name)) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n            ? inputValue > maxOutput.value\n            : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n            ? inputValue < minOutput.value\n            : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n      ? [path]\n      : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(object1: any, object2: any) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n      ? value === ''\n        ? NaN\n        : value\n          ? +value\n          : value\n      : valueAsDate && isString(value)\n        ? new Date(value)\n        : setValueAs\n          ? setValueAs(value)\n          : value;\n", "import { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "import {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n      ? rule.source\n      : isObject(rule)\n        ? isRegex(rule.value)\n          ? rule.value.source\n          : rule.value\n        : rule;\n", "import { Field, Validate } from '../types';\nimport isFunction from '../utils/isFunction';\nimport isObject from '../utils/isObject';\n\nconst ASYNC_FUNCTION = 'AsyncFunction';\n\nexport default (fieldReference: Field['_f']) =>\n  !!fieldReference &&\n  !!fieldReference.validate &&\n  !!(\n    (isFunction(fieldReference.validate) &&\n      fieldReference.validate.constructor.name === ASYNC_FUNCTION) ||\n    (isObject(fieldReference.validate) &&\n      Object.values(fieldReference.validate).find(\n        (validateFunction: Validate<unknown, unknown>) =>\n          validateFunction.constructor.name === ASYNC_FUNCTION,\n      ))\n  );\n", "import { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport {\n  BatchFieldArrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  PathValue,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasPromiseValidation from './hasPromiseValidation';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n>(\n  props: UseFormProps<TFieldValues, TContext> = {},\n): Omit<UseFormReturn<TFieldValues, TContext>, 'formState'> {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  let _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? {}\n    : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    disabled: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    values: createSubject(),\n    array: createSubject(),\n    state: createSubject(),\n  };\n  const validationModeBeforeSubmit = getValidationModes(_options.mode);\n  const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _updateValid = async (shouldUpdateValid?: boolean) => {\n    if (!_options.disabled && (_proxyFormState.isValid || shouldUpdateValid)) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _executeSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (names?: string[], isValidating?: boolean) => {\n    if (\n      !_options.disabled &&\n      (_proxyFormState.isValidating || _proxyFormState.validatingFields)\n    ) {\n      (names || Array.from(_names.mount)).forEach((name) => {\n        if (name) {\n          isValidating\n            ? set(_formState.validatingFields, name, isValidating)\n            : unset(_formState.validatingFields, name);\n        }\n      });\n\n      _subjects.state.next({\n        validatingFields: _formState.validatingFields,\n        isValidating: !isEmptyObject(_formState.validatingFields),\n      });\n    }\n  };\n\n  const _updateFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method && !_options.disabled) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        _proxyFormState.touchedFields &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _updateValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n\n    if (!_options.disabled) {\n      const disabledField = !!(\n        get(_fields, name) &&\n        get(_fields, name)._f &&\n        get(_fields, name)._f.disabled\n      );\n      if (!isBlurEvent || shouldDirty) {\n        if (_proxyFormState.isDirty) {\n          isPreviousDirty = _formState.isDirty;\n          _formState.isDirty = output.isDirty = _getDirty();\n          shouldUpdateField = isPreviousDirty !== output.isDirty;\n        }\n\n        const isCurrentFieldPristine =\n          disabledField || deepEqual(get(_defaultValues, name), fieldValue);\n\n        isPreviousDirty = !!(\n          !disabledField && get(_formState.dirtyFields, name)\n        );\n        isCurrentFieldPristine || disabledField\n          ? unset(_formState.dirtyFields, name)\n          : set(_formState.dirtyFields, name, true);\n        output.dirtyFields = _formState.dirtyFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          (_proxyFormState.dirtyFields &&\n            isPreviousDirty !== !isCurrentFieldPristine);\n      }\n\n      if (isBlurEvent) {\n        const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n        if (!isPreviousFieldTouched) {\n          set(_formState.touchedFields, name, isBlurEvent);\n          output.touchedFields = _formState.touchedFields;\n          shouldUpdateField =\n            shouldUpdateField ||\n            (_proxyFormState.touchedFields &&\n              isPreviousFieldTouched !== isBlurEvent);\n        }\n      }\n\n      shouldUpdateField && shouldRender && _subjects.state.next(output);\n    }\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      _proxyFormState.isValid &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (_options.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(_options.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n  };\n\n  const _executeSchema = async (name?: InternalFieldName[]) => {\n    _updateIsValidating(name, true);\n    const result = await _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n    _updateIsValidating(name);\n    return result;\n  };\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _executeSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field as Field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const isPromiseFunction =\n            field._f && hasPromiseValidation((field as Field)._f);\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name], true);\n          }\n\n          const fieldError = await validateField(\n            field as Field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (isPromiseFunction && _proxyFormState.validatingFields) {\n            _updateIsValidating([name]);\n          }\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        !isEmptyObject(fieldValue) &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) =>\n    !_options.disabled &&\n    (name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues));\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n            ? _defaultValues\n            : isString(names)\n              ? { [names]: defaultValue }\n              : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        _options.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.length > 1\n              ? fieldReference.refs.forEach(\n                  (checkboxRef) =>\n                    (!checkboxRef.defaultChecked || !checkboxRef.disabled) &&\n                    (checkboxRef.checked = Array.isArray(fieldValue)\n                      ? !!(fieldValue as []).find(\n                          (data: string) => data === checkboxRef.value,\n                        )\n                      : fieldValue === checkboxRef.value),\n                )\n              : fieldReference.refs[0] &&\n                (fieldReference.refs[0].checked = !!fieldValue);\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.values.next({\n              name,\n              values: { ..._formValues },\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      const fieldValue = value[fieldKey];\n      const fieldName = `${name}.${fieldKey}`;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        isObject(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: { ..._formValues },\n      });\n\n      if (\n        (_proxyFormState.isDirty || _proxyFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.values.next({\n      name: _state.mount ? name : undefined,\n      values: { ..._formValues },\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    _state.mount = true;\n    const target = event.target;\n    let name = target.name as string;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const getCurrentFieldValue = () =>\n      target.type ? getFieldValue(field._f) : getEventValue(event);\n    const _updateIsFieldValueUpdated = (fieldValue: any): void => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        (isDateObject(fieldValue) && isNaN(fieldValue.getTime())) ||\n        deepEqual(fieldValue, get(_formValues, name, fieldValue));\n    };\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = getCurrentFieldValue();\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(\n        name,\n        fieldValue,\n        isBlurEvent,\n        false,\n      );\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.values.next({\n          name,\n          type: event.type,\n          values: { ..._formValues },\n        });\n\n      if (shouldSkipValidation) {\n        if (_proxyFormState.isValid) {\n          if (_options.mode === 'onBlur' && isBlurEvent) {\n            _updateValid();\n          } else if (!isBlurEvent) {\n            _updateValid();\n          }\n        }\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      if (_options.resolver) {\n        const { errors } = await _executeSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        _updateIsValidating([name], true);\n        error = (\n          await validateField(\n            field,\n            _names.disabled,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n        _updateIsValidating([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _updateValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      (_proxyFormState.isValid && isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ...(_state.mount ? _formValues : _defaultValues),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n        ? get(values, fieldNames)\n        : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    error: get((formState || _formState).errors, name),\n    isValidating: !!get(_formState.validatingFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n    const currentError = get(_formState.errors, name) || {};\n\n    // Don't override existing error messages elsewhere in the object tree.\n    const { ref: currentRef, message, type, ...restOfErrorTree } = currentError;\n\n    set(_formState.errors, name, {\n      ...restOfErrorTree,\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.values.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.values.next({\n      values: { ..._formValues },\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _updateValid();\n  };\n\n  const _updateDisabledField: Control<TFieldValues>['_updateDisabledField'] = ({\n    disabled,\n    name,\n    field,\n    fields,\n  }) => {\n    if (\n      (isBoolean(disabled) && _state.mount) ||\n      !!disabled ||\n      _names.disabled.has(name)\n    ) {\n      disabled ? _names.disabled.add(name) : _names.disabled.delete(name);\n\n      updateTouchAndDirty(\n        name,\n        getFieldValue(field ? field._f : get(fields, name)._f),\n        false,\n        false,\n        true,\n      );\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined =\n      isBoolean(options.disabled) || isBoolean(_options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _updateDisabledField({\n        field,\n        disabled: isBoolean(options.disabled)\n          ? options.disabled\n          : _options.disabled,\n        name,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined\n        ? { disabled: options.disabled || _options.disabled }\n        : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          const currentField: Field = get(_fields, name);\n          if (currentField) {\n            ref.disabled = currentField._f.disabled || disabled;\n\n            if (Array.isArray(currentField._f.refs)) {\n              currentField._f.refs.forEach((inputRef) => {\n                inputRef.disabled = currentField._f.disabled || disabled;\n              });\n            }\n          }\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        e.persist && e.persist();\n      }\n\n      let fieldValues = cloneObject(_formValues);\n\n      if (_names.disabled.size) {\n        for (const name of _names.disabled) {\n          set(fieldValues, name, undefined);\n        }\n      }\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _executeSchema();\n        _formState.errors = errors;\n        fieldValues = values;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TFieldValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as PathValue<\n            TFieldValues,\n            FieldPath<TFieldValues>\n          >,\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _updateValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        const fieldsToCheck = new Set([\n          ..._names.mount,\n          ...Object.keys(getDirtyFields(_defaultValues, _formValues)),\n        ]);\n        for (const fieldName of Array.from(fieldsToCheck)) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        _fields = {};\n      }\n\n      _formValues = _options.shouldUnregister\n        ? keepStateOptions.keepDefaultValues\n          ? cloneObject(_defaultValues)\n          : {}\n        : cloneObject(values);\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.values.next({\n        values: { ...values },\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      disabled: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!_options.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n          ? _formState.isDirty\n          : !!(\n              keepStateOptions.keepDefaultValues &&\n              !deepEqual(formValues, _defaultValues)\n            ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? {}\n        : keepStateOptions.keepDirtyValues\n          ? keepStateOptions.keepDefaultValues && _formValues\n            ? getDirtyFields(_defaultValues, _formValues)\n            : _formState.dirtyFields\n          : keepStateOptions.keepDefaultValues && formValues\n            ? getDirtyFields(_defaultValues, formValues)\n            : keepStateOptions.keepDirty\n              ? _formState.dirtyFields\n              : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? (formValues as Function)(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect &&\n          isFunction(fieldRef.select) &&\n          fieldRef.select();\n      }\n    }\n  };\n\n  const _updateFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    (_options.defaultValues as Function)().then((values: TFieldValues) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  return {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _executeSchema,\n      _getWatch,\n      _getDirty,\n      _updateValid,\n      _removeUnmounted,\n      _updateFieldArray,\n      _updateDisabledField,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _updateFormState,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      _setErrors,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      set _formState(value) {\n        _formState = value;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n}\n", "import { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends\n    FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n>(\n  props: UseFieldArrayProps<TFieldValues, TFieldArrayName, TKeyName>,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n    rules,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  rules &&\n    (control as Control<TFieldValues>).register(\n      name as FieldPath<TFieldValues>,\n      rules as RegisterOptions<TFieldValues>,\n    );\n\n  useSubscribe({\n    next: ({\n      values,\n      name: fieldArrayName,\n    }: {\n      values?: FieldValues;\n      name?: InternalFieldName;\n    }) => {\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    },\n    subject: control._subjects.array,\n  });\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._updateFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    !Array.isArray(get(control._fields, name)) &&\n      set(control._fields, name, undefined);\n    control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted)\n    ) {\n      if (control._options.resolver) {\n        control._executeSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._names.disabled,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.values.next({\n      name,\n      values: { ...control._formValues },\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._updateValid();\n\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._updateFieldArray(name);\n\n    return () => {\n      (control._options.shouldUnregister || shouldUnregister) &&\n        control.unregister(name as FieldPath<TFieldValues>);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import React from 'react';\n\nimport { createFormControl } from './logic/createFormControl';\nimport getProxyFormState from './logic/getProxyFormState';\nimport shouldRenderFormState from './logic/shouldRenderFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues extends FieldValues | undefined = undefined,\n>(\n  props: UseFormProps<TFieldValues, TContext> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >(undefined);\n  const _values = React.useRef<typeof props.values>(undefined);\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...createFormControl(props),\n      formState,\n    };\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useSubscribe({\n    subject: control._subjects.state,\n    next: (\n      value: Partial<FormState<TFieldValues>> & { name?: InternalFieldName },\n    ) => {\n      if (\n        shouldRenderFormState(\n          value,\n          control._proxyFormState,\n          control._updateFormState,\n          true,\n        )\n      ) {\n        updateFormState({ ...control._formState });\n      }\n    },\n  });\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [props.values, control]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n    }\n  }, [props.errors, control]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._updateValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.values.next({\n        values: control._getWatch(),\n      });\n  }, [props.shouldUnregister, control]);\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "isNameInFieldArray", "names", "name", "has", "substring", "search", "getNodeParentName", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "isFileListInstance", "FileList", "Set", "Blob", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isPlainObject", "key", "compact", "filter", "Boolean", "isUndefined", "val", "undefined", "get", "object", "path", "defaultValue", "result", "split", "reduce", "isBoolean", "is<PERSON>ey", "test", "stringToPath", "input", "replace", "set", "index", "temp<PERSON>ath", "length", "lastIndex", "newValue", "objValue", "isNaN", "EVENTS", "VALIDATION_MODE", "INPUT_VALIDATION_RULES", "HookFormContext", "React", "createContext", "useFormContext", "useContext", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "isEmptyObject", "keys", "shouldRenderFormState", "formStateData", "updateFormState", "find", "convertToArrayPayload", "shouldSubscribeByName", "signalName", "exact", "some", "currentName", "startsWith", "useSubscribe", "props", "_props", "useRef", "current", "useEffect", "subscription", "disabled", "subject", "subscribe", "next", "unsubscribe", "useFormState", "methods", "useState", "_formState", "_mounted", "_localProxyFormState", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_name", "_updateFormState", "_subjects", "state", "_updateValid", "useMemo", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "useWatch", "values", "updateValue", "_formValues", "_getWatch", "_removeUnmounted", "useController", "shouldUnregister", "isArrayField", "array", "_registerProps", "register", "rules", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "field", "onChange", "onBlur", "ref", "elm", "_fields", "_f", "focus", "select", "setCustomValidity", "message", "reportValidity", "_shouldUnregisterField", "_options", "updateMounted", "mount", "_state", "action", "unregister", "_updateDisabledField", "fields", "flatten", "obj", "output", "nested", "nested<PERSON><PERSON>", "POST_REQUEST", "appendErrors", "validateAllFieldCriteria", "types", "generateId", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "options", "shouldFocus", "focusName", "focusIndex", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "isWatched", "isBlurEvent", "watchName", "slice", "iterateFieldsByAction", "fieldsNames", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "refs", "updateFieldArrayRootError", "fieldArrayErrors", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMessage", "isRadioInput", "isRegex", "RegExp", "defaultResult", "validResult", "getCheckboxValue", "option", "attributes", "defaultReturn", "getRadioValue", "previous", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "async", "disabled<PERSON>ieldN<PERSON>s", "shouldUseNativeValidation", "isFieldArray", "required", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "min", "max", "pattern", "validate", "valueAsNumber", "inputValue", "inputRef", "isRadio", "isCheckBox", "isRadioOrCheckbox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueDate", "valueAsDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "valueNumber", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "from", "to", "splice", "prependAt", "removeArrayAt", "indexes", "i", "temp", "removeAtIndexes", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "unset", "paths", "childObject", "updatePath", "baseGet", "isEmptyArray", "updateAt", "field<PERSON><PERSON><PERSON>", "createSubject", "_observers", "observers", "observer", "push", "o", "isPrimitive", "deepEqual", "object1", "object2", "getTime", "keys1", "keys2", "val1", "includes", "val2", "isMultipleSelect", "live", "isConnected", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "getFieldValueAs", "setValueAs", "NaN", "getFieldValue", "files", "selectedOptions", "getRuleValue", "rule", "source", "ASYNC_FUNCTION", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "pop", "defaultOptions", "reValidateMode", "shouldFocusError", "createFormControl", "delayError<PERSON><PERSON><PERSON>", "submitCount", "isSubmitted", "isSubmitting", "isSubmitSuccessful", "unMount", "timer", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldDisplayAllAssociatedErrors", "criteriaMode", "shouldUpdateValid", "resolver", "_executeSchema", "executeBuiltInValidation", "_updateIsValidating", "for<PERSON>ach", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "<PERSON><PERSON><PERSON>", "_getDirty", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "callback", "delayError", "updateErrors", "wait", "clearTimeout", "setTimeout", "updatedFormState", "context", "getResolverOptions", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "isPromiseFunction", "fieldReference", "validateFunction", "fieldError", "getV<PERSON>ues", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "_updateIsFieldValueUpdated", "Number", "shouldSkipValidation", "deps", "skipValidation", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "executeSchemaAndUpdateState", "Promise", "all", "getFieldState", "setError", "currentError", "currentRef", "restOfErrorTree", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "handleSubmit", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "size", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "fieldsToCheck", "form", "closest", "reset", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "_updateFieldArray", "method", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "argA", "argB", "unsetEmptyArray", "_getFieldArray", "_resetDefaultValues", "then", "resetOptions", "_disableForm", "_setErrors", "payload", "reset<PERSON>ield", "clearErrors", "inputName", "setFocus", "shouldSelect", "render", "mounted", "setMounted", "onSubmit", "children", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "flattenForm<PERSON><PERSON>ues", "append", "shouldStringifySubmissionData", "response", "fetch", "String", "body", "status", "createElement", "Fragment", "noValidate", "Provider", "keyName", "setFields", "ids", "_fieldIds", "_actioned", "fieldArrayName", "updateValues", "useCallback", "updatedFieldArrayValues", "existingError", "swap", "move", "prepend", "prependValue", "appendValue", "remove", "insertValue", "insertAt", "update", "item", "_formControl", "_values"], "mappings": "oCAEAA,EAAgBC,GACG,aAAjBA,EAAQC,KCHVC,EAAgBC,GAAkCA,aAAiBC,KCAnEC,EAAgBF,GAAuD,MAATA,ECGvD,MAAMG,EAAgBH,GACV,iBAAVA,EAET,IAAAI,EAAkCJ,IAC/BE,EAAkBF,KAClBK,MAAMC,QAAQN,IACfG,EAAaH,KACZD,EAAaC,GCLDO,EAACC,GACdJ,EAASI,IAAWA,EAAgBC,OAChCb,EAAiBY,EAAgBC,QAC9BD,EAAgBC,OAAOC,QACvBF,EAAgBC,OAAOT,MAC1BQ,ECNNG,EAAe,CAACC,EAA+BC,IAC7CD,EAAME,ICLO,CAACD,GACdA,EAAKE,UAAU,EAAGF,EAAKG,OAAO,iBAAmBH,EDIvCI,CAAkBJ,IELfK,EAAkB,oBAAXC,aACU,IAAvBA,OAAOC,aACM,oBAAbC,SCEe,SAAAC,EAAeC,GACrC,IAAIC,EACJ,MAAMlB,EAAUD,MAAMC,QAAQiB,GACxBE,EACgB,oBAAbC,UAA2BH,aAAgBG,SAEpD,GAAIH,aAAgBtB,KAClBuB,EAAO,IAAIvB,KAAKsB,QACX,GAAIA,aAAgBI,IACzBH,EAAO,IAAIG,IAAIJ,OACV,IACHL,IAAUK,aAAgBK,MAAQH,KACnCnB,IAAWF,EAASmB,GAcrB,OAAOA,EAVP,GAFAC,EAAOlB,EAAU,GAAK,GAEjBA,GClBM,CAACuB,IACd,MAAMC,EACJD,EAAWE,aAAeF,EAAWE,YAAYC,UAEnD,OACE5B,EAAS0B,IAAkBA,EAAcG,eAAe,gBACxD,EDYiBC,CAAcX,GAG7B,IAAK,MAAMY,KAAOZ,EACZA,EAAKU,eAAeE,KACtBX,EAAKW,GAAOb,EAAYC,EAAKY,UAJjCX,EAAOD,CAUV,CAED,OAAOC,CACT,CElCA,IAAAY,EAAwBpC,GACtBK,MAAMC,QAAQN,GAASA,EAAMqC,OAAOC,SAAW,GCDjDC,EAAgBC,QAA2CC,IAARD,ECKnDE,EAAe,CACbC,EACAC,EACAC,KAEA,IAAKD,IAASxC,EAASuC,GACrB,OAAOE,EAGT,MAAMC,EAASV,EAAQQ,EAAKG,MAAM,cAAcC,QAC9C,CAACF,EAAQX,IACPjC,EAAkB4C,GAAUA,EAASA,EAAOX,IAC9CQ,GAGF,OAAOJ,EAAYO,IAAWA,IAAWH,EACrCJ,EAAYI,EAAOC,IACjBC,EACAF,EAAOC,GACTE,CAAM,ECxBZG,EAAgBjD,GAAsD,kBAAVA,ECA7CkD,EAAClD,GAAkB,QAAQmD,KAAKnD,GCE/CoD,EAAgBC,GACdjB,EAAQiB,EAAMC,QAAQ,YAAa,IAAIP,MAAM,UCG/CQ,EAAe,CACbZ,EACAC,EACA5C,KAEA,IAAIwD,GAAS,EACb,MAAMC,EAAWP,EAAMN,GAAQ,CAACA,GAAQQ,EAAaR,GAC/Cc,EAASD,EAASC,OAClBC,EAAYD,EAAS,EAE3B,OAASF,EAAQE,GAAQ,CACvB,MAAMvB,EAAMsB,EAASD,GACrB,IAAII,EAAW5D,EAEf,GAAIwD,IAAUG,EAAW,CACvB,MAAME,EAAWlB,EAAOR,GACxByB,EACExD,EAASyD,IAAaxD,MAAMC,QAAQuD,GAChCA,EACCC,OAAOL,EAASD,EAAQ,IAEvB,GADA,EAET,CAED,GAAY,cAARrB,GAA+B,gBAARA,GAAiC,cAARA,EAClD,OAGFQ,EAAOR,GAAOyB,EACdjB,EAASA,EAAOR,EACjB,CACD,OAAOQ,CAAM,ECrCR,MAAMoB,EACL,OADKA,EAEA,WAFAA,EAGH,SAGGC,EACH,SADGA,EAED,WAFCA,EAGD,WAHCA,EAIA,YAJAA,EAKN,MAGMC,EACN,MADMA,EAEN,MAFMA,EAGA,YAHAA,EAIA,YAJAA,EAKF,UALEA,EAMD,WANCA,EAOD,WCjBNC,EAAkBC,EAAMC,cAAoC,MAgCrDC,EAAiB,IAK5BF,EAAMG,WAAWJ,GCtCnB,IAAeK,EAAA,CACbC,EACAC,EACAC,EACAC,GAAS,KAET,MAAM7B,EAAS,CACb8B,cAAeH,EAAQI,gBAGzB,IAAK,MAAM1C,KAAOqC,EAChBM,OAAOC,eAAejC,EAAQX,EAAK,CACjCO,IAAK,KACH,MAAMsC,EAAO7C,EAOb,OALIsC,EAAQQ,gBAAgBD,KAAUhB,IACpCS,EAAQQ,gBAAgBD,IAASL,GAAUX,GAG7CU,IAAwBA,EAAoBM,IAAQ,GAC7CR,EAAUQ,EAAK,IAK5B,OAAOlC,CAAM,ECxBfoC,EAAgBlF,GACdI,EAASJ,KAAW8E,OAAOK,KAAKnF,GAAO0D,OCK1B0B,EAAA,CACbC,EACAJ,EACAK,EACAX,KAEAW,EAAgBD,GAChB,MAAMxE,KAAEA,KAAS2D,GAAca,EAE/B,OACEH,EAAcV,IACdM,OAAOK,KAAKX,GAAWd,QAAUoB,OAAOK,KAAKF,GAAiBvB,QAC9DoB,OAAOK,KAAKX,GAAWe,MACpBpD,GACC8C,EAAgB9C,OACdwC,GAAUX,IAEhB,EC3BJwB,EAAmBxF,GAAcK,MAAMC,QAAQN,GAASA,EAAQ,CAACA,GCElDyF,EAAA,CACb5E,EACA6E,EACAC,KAEC9E,IACA6E,GACD7E,IAAS6E,GACTF,EAAsB3E,GAAM+E,MACzBC,GACCA,IACCF,EACGE,IAAgBH,EAChBG,EAAYC,WAAWJ,IACvBA,EAAWI,WAAWD,MCN1B,SAAUE,EAAgBC,GAC9B,MAAMC,EAAS9B,EAAM+B,OAAOF,GAC5BC,EAAOE,QAAUH,EAEjB7B,EAAMiC,WAAU,KACd,MAAMC,GACHL,EAAMM,UACPL,EAAOE,QAAQI,SACfN,EAAOE,QAAQI,QAAQC,UAAU,CAC/BC,KAAMR,EAAOE,QAAQM,OAGzB,MAAO,KACLJ,GAAgBA,EAAaK,aAAa,CAC3C,GACA,CAACV,EAAMM,UACZ,CCmBA,SAASK,EACPX,GAEA,MAAMY,EAAUvC,KACVI,QAAEA,EAAUmC,EAAQnC,QAAO6B,SAAEA,EAAQzF,KAAEA,EAAI8E,MAAEA,GAAUK,GAAS,IAC/DxB,EAAWc,GAAmBnB,EAAM0C,SAASpC,EAAQqC,YACtDC,EAAW5C,EAAM+B,QAAO,GACxBc,EAAuB7C,EAAM+B,OAAO,CACxCe,SAAS,EACTC,WAAW,EACXC,aAAa,EACbC,eAAe,EACfC,kBAAkB,EAClBC,cAAc,EACdC,SAAS,EACTC,QAAQ,IAEJC,EAAQtD,EAAM+B,OAAOrF,GAoC3B,OAlCA4G,EAAMtB,QAAUtF,EAEhBkF,EAAa,CACXO,WACAG,KACEzG,GAEA+G,EAASZ,SACTV,EACEgC,EAAMtB,QACNnG,EAAMa,KACN8E,IAEFP,EACEpF,EACAgH,EAAqBb,QACrB1B,EAAQiD,mBAEVpC,EAAgB,IACXb,EAAQqC,cACR9G,IAEPuG,QAAS9B,EAAQkD,UAAUC,QAG7BzD,EAAMiC,WAAU,KACdW,EAASZ,SAAU,EACnBa,EAAqBb,QAAQoB,SAAW9C,EAAQoD,cAAa,GAEtD,KACLd,EAASZ,SAAU,CAAK,IAEzB,CAAC1B,IAEGN,EAAM2D,SACX,IACEvD,EACEC,EACAC,EACAuC,EAAqBb,SACrB,IAEJ,CAAC3B,EAAWC,GAEhB,CC5GA,IAAAsD,EAAgB/H,GAAqD,iBAAVA,ECI5CgI,EAAA,CACbpH,EACAqH,EACAC,EACAC,EACAtF,IAEIkF,EAASnH,IACXuH,GAAYF,EAAOG,MAAMC,IAAIzH,GACtB8B,EAAIwF,EAAYtH,EAAOiC,IAG5BxC,MAAMC,QAAQM,GACTA,EAAM0H,KACVC,IACCJ,GAAYF,EAAOG,MAAMC,IAAIE,GAAY7F,EAAIwF,EAAYK,OAK/DJ,IAAaF,EAAOO,UAAW,GAExBN,GCqHH,SAAUO,EACdzC,GAEA,MAAMY,EAAUvC,KACVI,QACJA,EAAUmC,EAAQnC,QAAO5D,KACzBA,EAAIgC,aACJA,EAAYyD,SACZA,EAAQX,MACRA,GACEK,GAAS,GACPyB,EAAQtD,EAAM+B,OAAOrF,GAE3B4G,EAAMtB,QAAUtF,EAEhBkF,EAAa,CACXO,WACAC,QAAS9B,EAAQkD,UAAUe,OAC3BjC,KAAOjC,IAEHiB,EACEgC,EAAMtB,QACN3B,EAAU3D,KACV8E,IAGFgD,EACErH,EACE0G,EACEP,EAAMtB,QACN1B,EAAQwD,OACRzD,EAAUkE,QAAUjE,EAAQmE,aAC5B,EACA/F,IAIP,IAIL,MAAO7C,EAAO2I,GAAexE,EAAM0C,SACjCpC,EAAQoE,UACNhI,EACAgC,IAMJ,OAFAsB,EAAMiC,WAAU,IAAM3B,EAAQqE,qBAEvB9I,CACT,CClJM,SAAU+I,EAId/C,GAEA,MAAMY,EAAUvC,KACVxD,KAAEA,EAAIyF,SAAEA,EAAQ7B,QAAEA,EAAUmC,EAAQnC,QAAOuE,iBAAEA,GAAqBhD,EAClEiD,EAAetI,EAAmB8D,EAAQwD,OAAOiB,MAAOrI,GACxDb,EAAQyI,EAAS,CACrBhE,UACA5D,OACAgC,aAAcH,EACZ+B,EAAQmE,YACR/H,EACA6B,EAAI+B,EAAQI,eAAgBhE,EAAMmF,EAAMnD,eAE1C8C,OAAO,IAEHnB,EAAYmC,EAAa,CAC7BlC,UACA5D,OACA8E,OAAO,IAGHwD,EAAiBhF,EAAM+B,OAC3BzB,EAAQ2E,SAASvI,EAAM,IAClBmF,EAAMqD,MACTrJ,WACIiD,EAAU+C,EAAMM,UAAY,CAAEA,SAAUN,EAAMM,UAAa,MAI7DgD,EAAanF,EAAM2D,SACvB,IACEhD,OAAOyE,iBACL,GACA,CACEC,QAAS,CACPC,YAAY,EACZ/G,IAAK,MAAQA,EAAI8B,EAAUgD,OAAQ3G,IAErCoG,QAAS,CACPwC,YAAY,EACZ/G,IAAK,MAAQA,EAAI8B,EAAU2C,YAAatG,IAE1C6I,UAAW,CACTD,YAAY,EACZ/G,IAAK,MAAQA,EAAI8B,EAAU4C,cAAevG,IAE5CyG,aAAc,CACZmC,YAAY,EACZ/G,IAAK,MAAQA,EAAI8B,EAAU6C,iBAAkBxG,IAE/C8I,MAAO,CACLF,YAAY,EACZ/G,IAAK,IAAMA,EAAI8B,EAAUgD,OAAQ3G,OAIzC,CAAC2D,EAAW3D,IAGR+I,EAAQzF,EAAM2D,SAClB,KAAO,CACLjH,OACAb,WACIiD,EAAUqD,IAAa9B,EAAU8B,SACjC,CAAEA,SAAU9B,EAAU8B,UAAYA,GAClC,GACJuD,SAAWrJ,GACT2I,EAAehD,QAAQ0D,SAAS,CAC9BpJ,OAAQ,CACNT,MAAOO,EAAcC,GACrBK,KAAMA,GAERf,KAAMiE,IAEV+F,OAAQ,IACNX,EAAehD,QAAQ2D,OAAO,CAC5BrJ,OAAQ,CACNT,MAAO0C,EAAI+B,EAAQmE,YAAa/H,GAChCA,KAAMA,GAERf,KAAMiE,IAEVgG,IAAMC,IACJ,MAAMJ,EAAQlH,EAAI+B,EAAQwF,QAASpJ,GAE/B+I,GAASI,IACXJ,EAAMM,GAAGH,IAAM,CACbI,MAAO,IAAMH,EAAIG,QACjBC,OAAQ,IAAMJ,EAAII,SAClBC,kBAAoBC,GAClBN,EAAIK,kBAAkBC,GACxBC,eAAgB,IAAMP,EAAIO,kBAE7B,KAGL,CACE1J,EACA4D,EAAQmE,YACRtC,EACA9B,EAAU8B,SACVtG,EACAyE,EAAQwF,UA+CZ,OA3CA9F,EAAMiC,WAAU,KACd,MAAMoE,EACJ/F,EAAQgG,SAASzB,kBAAoBA,EAEjC0B,EAAgB,CAAC7J,EAAyBb,KAC9C,MAAM4J,EAAelH,EAAI+B,EAAQwF,QAASpJ,GAEtC+I,GAASA,EAAMM,KACjBN,EAAMM,GAAGS,MAAQ3K,EAClB,EAKH,GAFA0K,EAAc7J,GAAM,GAEhB2J,EAAwB,CAC1B,MAAMxK,EAAQsB,EAAYoB,EAAI+B,EAAQgG,SAAS7F,cAAe/D,IAC9D0C,EAAIkB,EAAQI,eAAgBhE,EAAMb,GAC9BuC,EAAYG,EAAI+B,EAAQmE,YAAa/H,KACvC0C,EAAIkB,EAAQmE,YAAa/H,EAAMb,EAElC,CAID,OAFCiJ,GAAgBxE,EAAQ2E,SAASvI,GAE3B,MAEHoI,EACIuB,IAA2B/F,EAAQmG,OAAOC,OAC1CL,GAEF/F,EAAQqG,WAAWjK,GACnB6J,EAAc7J,GAAM,EAAM,CAC/B,GACA,CAACA,EAAM4D,EAASwE,EAAcD,IAEjC7E,EAAMiC,WAAU,KACd3B,EAAQsG,qBAAqB,CAC3BzE,WACA0E,OAAQvG,EAAQwF,QAChBpJ,QACA,GACD,CAACyF,EAAUzF,EAAM4D,IAEbN,EAAM2D,SACX,KAAO,CACL8B,QACApF,YACA8E,gBAEF,CAACM,EAAOpF,EAAW8E,GAEvB,CCpKA,MCzCa2B,EAAWC,IACtB,MAAMC,EAAsB,CAAA,EAE5B,IAAK,MAAMhJ,KAAO2C,OAAOK,KAAK+F,GAC5B,GAAI/K,EAAa+K,EAAI/I,KAAsB,OAAb+I,EAAI/I,GAAe,CAC/C,MAAMiJ,EAASH,EAAQC,EAAI/I,IAE3B,IAAK,MAAMkJ,KAAavG,OAAOK,KAAKiG,GAClCD,EAAO,GAAGhJ,KAAOkJ,KAAeD,EAAOC,EAE1C,MACCF,EAAOhJ,GAAO+I,EAAI/I,GAItB,OAAOgJ,CAAM,ECbTG,EAAe,OCArB,IAAeC,EAAA,CACb1K,EACA2K,EACAhE,EACA1H,EACAwK,IAEAkB,EACI,IACKhE,EAAO3G,GACV4K,MAAO,IACDjE,EAAO3G,IAAS2G,EAAO3G,GAAO4K,MAAQjE,EAAO3G,GAAO4K,MAAQ,CAAA,EAChE3L,CAACA,GAAOwK,IAAW,IAGvB,CAAE,ECrBRoB,EAAe,KACb,MAAMC,EACmB,oBAAhBC,YAA8B3L,KAAK4L,MAA4B,IAApBD,YAAYC,MAEhE,MAAO,uCAAuCvI,QAAQ,SAAUwI,IAC9D,MAAMC,GAAqB,GAAhBC,KAAKC,SAAgBN,GAAK,GAAK,EAE1C,OAAa,KAALG,EAAWC,EAAS,EAAJA,EAAW,GAAKG,SAAS,GAAG,GACpD,ECLJC,EAAe,CACbtL,EACA2C,EACA4I,EAAiC,CAAE,IAEnCA,EAAQC,aAAe9J,EAAY6J,EAAQC,aACvCD,EAAQE,WACR,GAAGzL,KAAQ0B,EAAY6J,EAAQG,YAAc/I,EAAQ4I,EAAQG,cAC7D,GCRSC,EAACC,IAAsC,CACpDC,YAAaD,GAAQA,IAASzI,EAC9B2I,SAAUF,IAASzI,EACnB4I,WAAYH,IAASzI,EACrB6I,QAASJ,IAASzI,EAClB8I,UAAWL,IAASzI,ICNP+I,EAAA,CACblM,EACAoH,EACA+E,KAECA,IACA/E,EAAOO,UACNP,EAAOG,MAAMtH,IAAID,IACjB,IAAIoH,EAAOG,OAAOxC,MACfqH,GACCpM,EAAKiF,WAAWmH,IAChB,SAAS9J,KAAKtC,EAAKqM,MAAMD,EAAUvJ,YCT3C,MAAMyJ,EAAwB,CAC5BnC,EACAH,EACAuC,EACAC,KAEA,IAAK,MAAMlL,KAAOiL,GAAetI,OAAOK,KAAK6F,GAAS,CACpD,MAAMpB,EAAQlH,EAAIsI,EAAQ7I,GAE1B,GAAIyH,EAAO,CACT,MAAMM,GAAEA,KAAOoD,GAAiB1D,EAEhC,GAAIM,EAAI,CACN,GAAIA,EAAGqD,MAAQrD,EAAGqD,KAAK,IAAM1C,EAAOX,EAAGqD,KAAK,GAAIpL,KAASkL,EACvD,OAAO,EACF,GAAInD,EAAGH,KAAOc,EAAOX,EAAGH,IAAKG,EAAGrJ,QAAUwM,EAC/C,OAAO,EAEP,GAAIF,EAAsBG,EAAczC,GACtC,KAGL,MAAM,GAAIzK,EAASkN,IACdH,EAAsBG,EAA2BzC,GACnD,KAGL,CACF,CACM,ECvBT,IAAA2C,EAAe,CACbhG,EACAmC,EACA9I,KAEA,MAAM4M,EAAmBjI,EAAsB9C,EAAI8E,EAAQ3G,IAG3D,OAFA0C,EAAIkK,EAAkB,OAAQ9D,EAAM9I,IACpC0C,EAAIiE,EAAQ3G,EAAM4M,GACXjG,CAAM,EChBfkG,GAAgB7N,GACG,SAAjBA,EAAQC,KCHV6N,GAAgB3N,GACG,mBAAVA,ECCM4N,GAAC5N,IACd,IAAKkB,EACH,OAAO,EAGT,MAAM2M,EAAQ7N,EAAUA,EAAsB8N,cAA6B,EAC3E,OACE9N,aACC6N,GAASA,EAAME,YAAcF,EAAME,YAAY3M,YAAcA,YAC9D,ECRJ4M,GAAgBhO,GAAqC+H,EAAS/H,GCD9DiO,GAAgBpO,GACG,UAAjBA,EAAQC,KCHVoO,GAAgBlO,GAAoCA,aAAiBmO,OCOrE,MAAMC,GAAqC,CACzCpO,OAAO,EACPuH,SAAS,GAGL8G,GAAc,CAAErO,OAAO,EAAMuH,SAAS,GAE5C,IAAe+G,GAAClC,IACd,GAAI/L,MAAMC,QAAQ8L,GAAU,CAC1B,GAAIA,EAAQ1I,OAAS,EAAG,CACtB,MAAMgF,EAAS0D,EACZ/J,QAAQkM,GAAWA,GAAUA,EAAO7N,UAAY6N,EAAOjI,WACvDgC,KAAKiG,GAAWA,EAAOvO,QAC1B,MAAO,CAAEA,MAAO0I,EAAQnB,UAAWmB,EAAOhF,OAC3C,CAED,OAAO0I,EAAQ,GAAG1L,UAAY0L,EAAQ,GAAG9F,SAErC8F,EAAQ,GAAGoC,aAAejM,EAAY6J,EAAQ,GAAGoC,WAAWxO,OAC1DuC,EAAY6J,EAAQ,GAAGpM,QAA+B,KAArBoM,EAAQ,GAAGpM,MAC1CqO,GACA,CAAErO,MAAOoM,EAAQ,GAAGpM,MAAOuH,SAAS,GACtC8G,GACFD,EACL,CAED,OAAOA,EAAa,EC5BtB,MAAMK,GAAkC,CACtClH,SAAS,EACTvH,MAAO,MAGT,IAAe0O,GAACtC,GACd/L,MAAMC,QAAQ8L,GACVA,EAAQpJ,QACN,CAAC2L,EAAUJ,IACTA,GAAUA,EAAO7N,UAAY6N,EAAOjI,SAChC,CACEiB,SAAS,EACTvH,MAAOuO,EAAOvO,OAEhB2O,GACNF,IAEFA,GClBQ,SAAUG,GACtB9L,EACAiH,EACAjK,EAAO,YAEP,GACEkO,GAAUlL,IACTzC,MAAMC,QAAQwC,IAAWA,EAAO+L,MAAMb,KACtC/K,EAAUH,KAAYA,EAEvB,MAAO,CACLhD,OACAwK,QAAS0D,GAAUlL,GAAUA,EAAS,GACtCiH,MAGN,CChBA,IAAA+E,GAAgBC,GACd3O,EAAS2O,KAAoBb,GAAQa,GACjCA,EACA,CACE/O,MAAO+O,EACPzE,QAAS,ICwBjB0E,GAAeC,MACbrF,EACAsF,EACAhH,EACAsD,EACA2D,EACAC,KAEA,MAAMrF,IACJA,EAAGwD,KACHA,EAAI8B,SACJA,EAAQC,UACRA,EAASC,UACTA,EAASC,IACTA,EAAGC,IACHA,EAAGC,QACHA,EAAOC,SACPA,EAAQ9O,KACRA,EAAI+O,cACJA,EAAajF,MACbA,GACEf,EAAMM,GACJ2F,EAA+BnN,EAAIwF,EAAYrH,GACrD,IAAK8J,GAASuE,EAAmBpO,IAAID,GACnC,MAAO,GAET,MAAMiP,EAA6BvC,EAAOA,EAAK,GAAMxD,EAC/CM,EAAqBC,IACrB6E,GAA6BW,EAASvF,iBACxCuF,EAASzF,kBAAkBpH,EAAUqH,GAAW,GAAKA,GAAW,IAChEwF,EAASvF,iBACV,EAEGZ,EAA6B,CAAA,EAC7BoG,EAAU9B,GAAalE,GACvBiG,EAAapQ,EAAgBmK,GAC7BkG,EAAoBF,GAAWC,EAC/BE,GACFN,GAAiBlC,GAAY3D,KAC7BxH,EAAYwH,EAAI/J,QAChBuC,EAAYsN,IACbjC,GAAc7D,IAAsB,KAAdA,EAAI/J,OACZ,KAAf6P,GACCxP,MAAMC,QAAQuP,KAAgBA,EAAWnM,OACtCyM,EAAoB5E,EAAa6E,KACrC,KACAvP,EACA2K,EACA7B,GAEI0G,EAAmB,CACvBC,EACAC,EACAC,EACAC,EAAmBxM,EACnByM,EAAmBzM,KAEnB,MAAMqG,EAAUgG,EAAYC,EAAmBC,EAC/C7G,EAAM9I,GAAQ,CACZf,KAAMwQ,EAAYG,EAAUC,EAC5BpG,UACAP,SACGoG,EAAkBG,EAAYG,EAAUC,EAASpG,GACrD,EAGH,GACE8E,GACK/O,MAAMC,QAAQuP,KAAgBA,EAAWnM,OAC1C2L,KACGY,IAAsBC,GAAWhQ,EAAkB2P,KACnD5M,EAAU4M,KAAgBA,GAC1BG,IAAe1B,GAAiBf,GAAMhG,SACtCwI,IAAYrB,GAAcnB,GAAMhG,SACvC,CACA,MAAMvH,MAAEA,EAAKsK,QAAEA,GAAY0D,GAAUqB,GACjC,CAAErP,QAASqP,EAAU/E,QAAS+E,GAC9BP,GAAmBO,GAEvB,GAAIrP,IACF2J,EAAM9I,GAAQ,CACZf,KAAMmE,EACNqG,UACAP,IAAK+F,KACFK,EAAkBlM,EAAiCqG,KAEnDkB,GAEH,OADAnB,EAAkBC,GACXX,CAGZ,CAED,KAAKuG,GAAahQ,EAAkBsP,IAAStP,EAAkBuP,IAAO,CACpE,IAAIa,EACAK,EACJ,MAAMC,EAAY9B,GAAmBW,GAC/BoB,EAAY/B,GAAmBU,GAErC,GAAKtP,EAAkB2P,IAAgB/L,MAAM+L,GAUtC,CACL,MAAMiB,EACH/G,EAAyBgH,aAAe,IAAI9Q,KAAK4P,GAC9CmB,EAAqBC,GACzB,IAAIhR,MAAK,IAAIA,MAAOiR,eAAiB,IAAMD,GACvCE,EAAqB,QAAZpH,EAAIjK,KACbsR,EAAqB,QAAZrH,EAAIjK,KAEfiI,EAAS6I,EAAU5Q,QAAU6P,IAC/BS,EAAYa,EACRH,EAAkBnB,GAAcmB,EAAkBJ,EAAU5Q,OAC5DoR,EACEvB,EAAae,EAAU5Q,MACvB8Q,EAAY,IAAI7Q,KAAK2Q,EAAU5Q,QAGnC+H,EAAS8I,EAAU7Q,QAAU6P,IAC/Bc,EAAYQ,EACRH,EAAkBnB,GAAcmB,EAAkBH,EAAU7Q,OAC5DoR,EACEvB,EAAagB,EAAU7Q,MACvB8Q,EAAY,IAAI7Q,KAAK4Q,EAAU7Q,OAExC,KAjCmE,CAClE,MAAMqR,EACHtH,EAAyB6F,gBACzBC,GAAcA,EAAaA,GACzB3P,EAAkB0Q,EAAU5Q,SAC/BsQ,EAAYe,EAAcT,EAAU5Q,OAEjCE,EAAkB2Q,EAAU7Q,SAC/B2Q,EAAYU,EAAcR,EAAU7Q,MAEvC,CAyBD,IAAIsQ,GAAaK,KACfN,IACIC,EACFM,EAAUtG,QACVuG,EAAUvG,QACVrG,EACAA,IAEGuH,GAEH,OADAnB,EAAkBV,EAAM9I,GAAOyJ,SACxBX,CAGZ,CAED,IACG2F,GAAaC,KACbW,IACAnI,EAAS8H,IAAgBT,GAAgB/O,MAAMC,QAAQuP,IACxD,CACA,MAAMyB,EAAkBxC,GAAmBQ,GACrCiC,EAAkBzC,GAAmBS,GACrCe,GACHpQ,EAAkBoR,EAAgBtR,QACnC6P,EAAWnM,QAAU4N,EAAgBtR,MACjC2Q,GACHzQ,EAAkBqR,EAAgBvR,QACnC6P,EAAWnM,QAAU6N,EAAgBvR,MAEvC,IAAIsQ,GAAaK,KACfN,EACEC,EACAgB,EAAgBhH,QAChBiH,EAAgBjH,UAEbkB,GAEH,OADAnB,EAAkBV,EAAM9I,GAAOyJ,SACxBX,CAGZ,CAED,GAAI+F,IAAYQ,GAAWnI,EAAS8H,GAAa,CAC/C,MAAQ7P,MAAOwR,EAAYlH,QAAEA,GAAYwE,GAAmBY,GAE5D,GAAIxB,GAAQsD,KAAkB3B,EAAW4B,MAAMD,KAC7C7H,EAAM9I,GAAQ,CACZf,KAAMmE,EACNqG,UACAP,SACGoG,EAAkBlM,EAAgCqG,KAElDkB,GAEH,OADAnB,EAAkBC,GACXX,CAGZ,CAED,GAAIgG,EACF,GAAIhC,GAAWgC,GAAW,CACxB,MACM+B,EAAgB9C,SADDe,EAASE,EAAY3H,GACK4H,GAE/C,GAAI4B,IACF/H,EAAM9I,GAAQ,IACT6Q,KACAvB,EACDlM,EACAyN,EAAcpH,WAGbkB,GAEH,OADAnB,EAAkBqH,EAAcpH,SACzBX,CAGZ,MAAM,GAAIvJ,EAASuP,GAAW,CAC7B,IAAIgC,EAAmB,CAAA,EAEvB,IAAK,MAAMxP,KAAOwN,EAAU,CAC1B,IAAKzK,EAAcyM,KAAsBnG,EACvC,MAGF,MAAMkG,EAAgB9C,SACde,EAASxN,GAAK0N,EAAY3H,GAChC4H,EACA3N,GAGEuP,IACFC,EAAmB,IACdD,KACAvB,EAAkBhO,EAAKuP,EAAcpH,UAG1CD,EAAkBqH,EAAcpH,SAE5BkB,IACF7B,EAAM9I,GAAQ8Q,GAGnB,CAED,IAAKzM,EAAcyM,KACjBhI,EAAM9I,GAAQ,CACZkJ,IAAK+F,KACF6B,IAEAnG,GACH,OAAO7B,CAGZ,CAIH,OADAU,GAAkB,GACXV,CAAK,EC3RdiI,GAAe,CAAIrQ,EAAWvB,IAAwB,IACjDuB,KACAiE,EAAsBxF,ICJ3B6R,GAAmB7R,GACjBK,MAAMC,QAAQN,GAASA,EAAMsI,KAAI,KAAe,SAAI7F,ECO9B,SAAAqP,GACtBvQ,EACAiC,EACAxD,GAEA,MAAO,IACFuB,EAAK2L,MAAM,EAAG1J,MACdgC,EAAsBxF,MACtBuB,EAAK2L,MAAM1J,GAElB,CChBA,IAAAuO,GAAe,CACbxQ,EACAyQ,EACAC,IAEK5R,MAAMC,QAAQiB,IAIfgB,EAAYhB,EAAK0Q,MACnB1Q,EAAK0Q,QAAMxP,GAEblB,EAAK2Q,OAAOD,EAAI,EAAG1Q,EAAK2Q,OAAOF,EAAM,GAAG,IAEjCzQ,GARE,GCNX4Q,GAAe,CAAI5Q,EAAWvB,IAAwB,IACjDwF,EAAsBxF,MACtBwF,EAAsBjE,ICY3B,IAAe6Q,GAAA,CAAI7Q,EAAWiC,IAC5BjB,EAAYiB,GACR,GAdN,SAA4BjC,EAAW8Q,GACrC,IAAIC,EAAI,EACR,MAAMC,EAAO,IAAIhR,GAEjB,IAAK,MAAMiC,KAAS6O,EAClBE,EAAKL,OAAO1O,EAAQ8O,EAAG,GACvBA,IAGF,OAAOlQ,EAAQmQ,GAAM7O,OAAS6O,EAAO,EACvC,CAKMC,CACEjR,EACCiE,EAAsBhC,GAAoBiP,MAAK,CAACC,EAAGC,IAAMD,EAAIC,KCrBtEC,GAAe,CAAIrR,EAAWsR,EAAgBC,MAC3CvR,EAAKsR,GAAStR,EAAKuR,IAAW,CAACvR,EAAKuR,GAASvR,EAAKsR,GAAQ,ECyB/C,SAAUE,GAAMpQ,EAAaC,GACzC,MAAMoQ,EAAQ3S,MAAMC,QAAQsC,GACxBA,EACAM,EAAMN,GACJ,CAACA,GACDQ,EAAaR,GAEbqQ,EAA+B,IAAjBD,EAAMtP,OAAef,EA3B3C,SAAiBA,EAAauQ,GAC5B,MAAMxP,EAASwP,EAAWhG,MAAM,GAAI,GAAGxJ,OACvC,IAAIF,EAAQ,EAEZ,KAAOA,EAAQE,GACbf,EAASJ,EAAYI,GAAUa,IAAUb,EAAOuQ,EAAW1P,MAG7D,OAAOb,CACT,CAkBoDwQ,CAAQxQ,EAAQqQ,GAE5DxP,EAAQwP,EAAMtP,OAAS,EACvBvB,EAAM6Q,EAAMxP,GAclB,OAZIyP,UACKA,EAAY9Q,GAIT,IAAVqB,IACEpD,EAAS6S,IAAgB/N,EAAc+N,IACtC5S,MAAMC,QAAQ2S,IA5BrB,SAAsB/H,GACpB,IAAK,MAAM/I,KAAO+I,EAChB,GAAIA,EAAIjJ,eAAeE,KAASI,EAAY2I,EAAI/I,IAC9C,OAAO,EAGX,OAAO,CACT,CAqBqCiR,CAAaH,KAE9CF,GAAMpQ,EAAQqQ,EAAM9F,MAAM,GAAI,IAGzBvK,CACT,CCnDA,IAAA0Q,GAAe,CAAIC,EAAkB9P,EAAexD,KAClDsT,EAAY9P,GAASxD,EACdsT,GCcT,IAAAC,GAAe,KACb,IAAIC,EAA4B,GAqBhC,MAAO,CACL,aAAIC,GACF,OAAOD,CACR,EACD/M,KAvBYzG,IACZ,IAAK,MAAM0T,KAAYF,EACrBE,EAASjN,MAAQiN,EAASjN,KAAKzG,EAChC,EAqBDwG,UAlBiBkN,IACjBF,EAAWG,KAAKD,GACT,CACLhN,YAAa,KACX8M,EAAaA,EAAWnR,QAAQuR,GAAMA,IAAMF,GAAS,IAezDhN,YAVkB,KAClB8M,EAAa,EAAE,EAUhB,ECxCHK,GAAgB7T,GACdE,EAAkBF,KAAWG,EAAaH,GCD9B,SAAU8T,GAAUC,EAAcC,GAC9C,GAAIH,GAAYE,IAAYF,GAAYG,GACtC,OAAOD,IAAYC,EAGrB,GAAIjU,EAAagU,IAAYhU,EAAaiU,GACxC,OAAOD,EAAQE,YAAcD,EAAQC,UAGvC,MAAMC,EAAQpP,OAAOK,KAAK4O,GACpBI,EAAQrP,OAAOK,KAAK6O,GAE1B,GAAIE,EAAMxQ,SAAWyQ,EAAMzQ,OACzB,OAAO,EAGT,IAAK,MAAMvB,KAAO+R,EAAO,CACvB,MAAME,EAAOL,EAAQ5R,GAErB,IAAKgS,EAAME,SAASlS,GAClB,OAAO,EAGT,GAAY,QAARA,EAAe,CACjB,MAAMmS,EAAON,EAAQ7R,GAErB,GACGpC,EAAaqU,IAASrU,EAAauU,IACnClU,EAASgU,IAAShU,EAASkU,IAC3BjU,MAAMC,QAAQ8T,IAAS/T,MAAMC,QAAQgU,IACjCR,GAAUM,EAAME,GACjBF,IAASE,EAEb,OAAO,CAEV,CACF,CAED,OAAO,CACT,CC1CA,IAAeC,GAAC1U,GACG,oBAAjBA,EAAQC,KCCK0U,GAACzK,GAAa6D,GAAc7D,IAAQA,EAAI0K,YCFxCC,GAAInT,IACjB,IAAK,MAAMY,KAAOZ,EAChB,GAAIoM,GAAWpM,EAAKY,IAClB,OAAO,EAGX,OAAO,CAAK,ECDd,SAASwS,GAAmBpT,EAASyJ,EAA8B,IACjE,MAAM4J,EAAoBvU,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAASqT,EACpB,IAAK,MAAMzS,KAAOZ,EAEdlB,MAAMC,QAAQiB,EAAKY,KAClB/B,EAASmB,EAAKY,MAAUuS,GAAkBnT,EAAKY,KAEhD6I,EAAO7I,GAAO9B,MAAMC,QAAQiB,EAAKY,IAAQ,GAAK,GAC9CwS,GAAgBpT,EAAKY,GAAM6I,EAAO7I,KACxBjC,EAAkBqB,EAAKY,MACjC6I,EAAO7I,IAAO,GAKpB,OAAO6I,CACT,CAEA,SAAS6J,GACPtT,EACA2G,EACA4M,GAKA,MAAMF,EAAoBvU,MAAMC,QAAQiB,GAExC,GAAInB,EAASmB,IAASqT,EACpB,IAAK,MAAMzS,KAAOZ,EAEdlB,MAAMC,QAAQiB,EAAKY,KAClB/B,EAASmB,EAAKY,MAAUuS,GAAkBnT,EAAKY,IAG9CI,EAAY2F,IACZ2L,GAAYiB,EAAsB3S,IAElC2S,EAAsB3S,GAAO9B,MAAMC,QAAQiB,EAAKY,IAC5CwS,GAAgBpT,EAAKY,GAAM,IAC3B,IAAKwS,GAAgBpT,EAAKY,KAE9B0S,GACEtT,EAAKY,GACLjC,EAAkBgI,GAAc,GAAKA,EAAW/F,GAChD2S,EAAsB3S,IAI1B2S,EAAsB3S,IAAQ2R,GAAUvS,EAAKY,GAAM+F,EAAW/F,IAKpE,OAAO2S,CACT,CAEA,IAAAC,GAAe,CAAInQ,EAAkBsD,IACnC2M,GACEjQ,EACAsD,EACAyM,GAAgBzM,IClEpB8M,GAAe,CACbhV,GACE4P,gBAAemB,cAAakE,gBAE9B1S,EAAYvC,GACRA,EACA4P,EACY,KAAV5P,EACEkV,IACAlV,GACGA,EACDA,EACJ+Q,GAAehJ,EAAS/H,GACtB,IAAIC,KAAKD,GACTiV,EACEA,EAAWjV,GACXA,ECTY,SAAAmV,GAAcjL,GACpC,MAAMH,EAAMG,EAAGH,IAEf,OAAI2D,GAAY3D,GACPA,EAAIqL,MAGTnH,GAAalE,GACR2E,GAAcxE,EAAGqD,MAAMvN,MAG5BuU,GAAiBxK,GACZ,IAAIA,EAAIsL,iBAAiB/M,KAAI,EAAGtI,WAAYA,IAGjDgQ,EAAWjG,GACNuE,GAAiBpE,EAAGqD,MAAMvN,MAG5BgV,GAAgBzS,EAAYwH,EAAI/J,OAASkK,EAAGH,IAAI/J,MAAQ+J,EAAI/J,MAAOkK,EAC5E,CCpBA,ICFAoL,GACEC,GAEAhT,EAAYgT,GACRA,EACArH,GAAQqH,GACNA,EAAKC,OACLpV,EAASmV,GACPrH,GAAQqH,EAAKvV,OACXuV,EAAKvV,MAAMwV,OACXD,EAAKvV,MACPuV,EChBV,MAAME,GAAiB,gBCAC,SAAAC,GACtBlO,EACAyC,EACApJ,GAKA,MAAM8I,EAAQjH,EAAI8E,EAAQ3G,GAE1B,GAAI8I,GAASzG,EAAMrC,GACjB,MAAO,CACL8I,QACA9I,QAIJ,MAAMD,EAAQC,EAAKkC,MAAM,KAEzB,KAAOnC,EAAM8C,QAAQ,CACnB,MAAM6E,EAAY3H,EAAM+U,KAAK,KACvB/L,EAAQlH,EAAIuH,EAAS1B,GACrBqN,EAAalT,EAAI8E,EAAQe,GAE/B,GAAIqB,IAAUvJ,MAAMC,QAAQsJ,IAAU/I,IAAS0H,EAC7C,MAAO,CAAE1H,QAGX,GAAI+U,GAAcA,EAAW9V,KAC3B,MAAO,CACLe,KAAM0H,EACNoB,MAAOiM,GAIXhV,EAAMiV,KACP,CAED,MAAO,CACLhV,OAEJ,CC0CA,MAAMiV,GAAiB,CACrBrJ,KAAMzI,EACN+R,eAAgB/R,EAChBgS,kBAAkB,GAGJ,SAAAC,GAIdjQ,EAA8C,IAE9C,IAuCIkQ,EAvCAzL,EAAW,IACVqL,MACA9P,GAEDc,EAAsC,CACxCqP,YAAa,EACblP,SAAS,EACTC,UAAWyG,GAAWlD,EAAS7F,eAC/B0C,cAAc,EACd8O,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpB/O,SAAS,EACTH,cAAe,CAAE,EACjBD,YAAa,CAAE,EACfE,iBAAkB,CAAE,EACpBG,OAAQiD,EAASjD,QAAU,CAAE,EAC7BlB,SAAUmE,EAASnE,WAAY,GAE7B2D,EAAqB,CAAA,EACrBpF,GACFzE,EAASqK,EAAS7F,gBAAkBxE,EAASqK,EAAS/B,UAClDpH,EAAYmJ,EAAS7F,eAAiB6F,EAAS/B,SAC/C,GACFE,EAAc6B,EAASzB,iBACvB,CAAE,EACF1H,EAAYuD,GACZ+F,EAAS,CACXC,QAAQ,EACRF,OAAO,EACPvC,OAAO,GAELH,EAAgB,CAClB0C,MAAO,IAAIhJ,IACX2E,SAAU,IAAI3E,IACd4U,QAAS,IAAI5U,IACbuH,MAAO,IAAIvH,IACXyG,MAAO,IAAIzG,KAGT6U,EAAQ,EACZ,MAAMvR,EAAiC,CACrCgC,SAAS,EACTE,aAAa,EACbE,kBAAkB,EAClBD,eAAe,EACfE,cAAc,EACdC,SAAS,EACTC,QAAQ,GAEJG,EAAoC,CACxCe,OAAQ6K,KACRrK,MAAOqK,KACP3L,MAAO2L,MAEHkD,EAA6BjK,EAAmB/B,EAASgC,MACzDiK,EAA4BlK,EAAmB/B,EAASsL,gBACxDY,EACJlM,EAASmM,eAAiB5S,EAStB6D,EAAeoH,MAAO4H,IAC1B,IAAKpM,EAASnE,WAAarB,EAAgBsC,SAAWsP,GAAoB,CACxE,MAAMtP,EAAUkD,EAASqM,SACrB5R,SAAqB6R,KAAkBvP,cACjCwP,EAAyB/M,GAAS,GAExC1C,IAAYT,EAAWS,SACzBI,EAAUC,MAAMnB,KAAK,CACnBc,WAGL,GAGG0P,EAAsB,CAACrW,EAAkB0G,KAE1CmD,EAASnE,WACTrB,EAAgBqC,eAAgBrC,EAAgBoC,oBAEhDzG,GAASP,MAAM2R,KAAK/J,EAAO0C,QAAQuM,SAASrW,IACvCA,IACFyG,EACI/D,EAAIuD,EAAWO,iBAAkBxG,EAAMyG,GACvCyL,GAAMjM,EAAWO,iBAAkBxG,GACxC,IAGH8G,EAAUC,MAAMnB,KAAK,CACnBY,iBAAkBP,EAAWO,iBAC7BC,cAAepC,EAAc4B,EAAWO,oBAE3C,EA2EG8P,EAAsB,CAC1BtW,EACAuW,EACApX,EACA+J,KAEA,MAAMH,EAAelH,EAAIuH,EAASpJ,GAElC,GAAI+I,EAAO,CACT,MAAM/G,EAAeH,EACnBkG,EACA/H,EACA0B,EAAYvC,GAAS0C,EAAImC,EAAgBhE,GAAQb,GAGnDuC,EAAYM,IACXkH,GAAQA,EAAyBsN,gBAClCD,EACI7T,EACEqF,EACA/H,EACAuW,EAAuBvU,EAAesS,GAAcvL,EAAMM,KAE5DoN,EAAczW,EAAMgC,GAExB+H,EAAOD,OAAS9C,GACjB,GAGG0P,EAAsB,CAC1B1W,EACA2W,EACAxK,EACAyK,EACAC,KAIA,IAAIC,GAAoB,EACpBC,GAAkB,EACtB,MAAMzM,EAA8D,CAClEtK,QAGF,IAAK4J,EAASnE,SAAU,CACtB,MAAMuR,KACJnV,EAAIuH,EAASpJ,IACb6B,EAAIuH,EAASpJ,GAAMqJ,IACnBxH,EAAIuH,EAASpJ,GAAMqJ,GAAG5D,UAExB,IAAK0G,GAAeyK,EAAa,CAC3BxS,EAAgBgC,UAClB2Q,EAAkB9Q,EAAWG,QAC7BH,EAAWG,QAAUkE,EAAOlE,QAAU6Q,IACtCH,EAAoBC,IAAoBzM,EAAOlE,SAGjD,MAAM8Q,EACJF,GAAiB/D,GAAUpR,EAAImC,EAAgBhE,GAAO2W,GAExDI,IACGC,IAAiBnV,EAAIoE,EAAWK,YAAatG,IAEhDkX,GAA0BF,EACtB9E,GAAMjM,EAAWK,YAAatG,GAC9B0C,EAAIuD,EAAWK,YAAatG,GAAM,GACtCsK,EAAOhE,YAAcL,EAAWK,YAChCwQ,EACEA,GACC1S,EAAgBkC,aACfyQ,KAAqBG,CAC1B,CAED,GAAI/K,EAAa,CACf,MAAMgL,EAAyBtV,EAAIoE,EAAWM,cAAevG,GAExDmX,IACHzU,EAAIuD,EAAWM,cAAevG,EAAMmM,GACpC7B,EAAO/D,cAAgBN,EAAWM,cAClCuQ,EACEA,GACC1S,EAAgBmC,eACf4Q,IAA2BhL,EAElC,CAED2K,GAAqBD,GAAgB/P,EAAUC,MAAMnB,KAAK0E,EAC3D,CAED,OAAOwM,EAAoBxM,EAAS,EAAE,EAGlC8M,EAAsB,CAC1BpX,EACA0G,EACAoC,EACAL,KAMA,MAAM4O,EAAqBxV,EAAIoE,EAAWU,OAAQ3G,GAC5CgW,EACJ5R,EAAgBsC,SAChBtE,EAAUsE,IACVT,EAAWS,UAAYA,EA1NzB,IAAqB4Q,EAuOrB,GAXI1N,EAAS2N,YAAczO,GA5NNwO,EA6NW,IA5Hb,EAACtX,EAAyB8I,KAC7CpG,EAAIuD,EAAWU,OAAQ3G,EAAM8I,GAC7BhC,EAAUC,MAAMnB,KAAK,CACnBe,OAAQV,EAAWU,QACnB,EAwHoC6Q,CAAaxX,EAAM8I,GAAvDuM,EA5NDoC,IACCC,aAAa/B,GACbA,EAAQgC,WAAWL,EAAUG,EAAK,EA2NlCpC,EAAmBzL,EAAS2N,cAE5BG,aAAa/B,GACbN,EAAqB,KACrBvM,EACIpG,EAAIuD,EAAWU,OAAQ3G,EAAM8I,GAC7BoJ,GAAMjM,EAAWU,OAAQ3G,KAI5B8I,GAASmK,GAAUoE,EAAoBvO,GAASuO,KAChDhT,EAAcoE,IACfuN,EACA,CACA,MAAM4B,EAAmB,IACpBnP,KACCuN,GAAqB5T,EAAUsE,GAAW,CAAEA,WAAY,GAC5DC,OAAQV,EAAWU,OACnB3G,QAGFiG,EAAa,IACRA,KACA2R,GAGL9Q,EAAUC,MAAMnB,KAAKgS,EACtB,GAGG1B,EAAiB9H,MAAOpO,IAC5BoW,EAAoBpW,GAAM,GAC1B,MAAMiC,QAAe2H,EAASqM,SAC5BlO,EACA6B,EAASiO,QJrZA,EACbtL,EACAnD,EACA2M,EACAzH,KAEA,MAAMnE,EAAiD,CAAA,EAEvD,IAAK,MAAMnK,KAAQuM,EAAa,CAC9B,MAAMxD,EAAelH,EAAIuH,EAASpJ,GAElC+I,GAASrG,EAAIyH,EAAQnK,EAAM+I,EAAMM,GAClC,CAED,MAAO,CACL0M,eACAhW,MAAO,IAAIwM,GACXpC,SACAmE,4BACD,EImYGwJ,CACE9X,GAAQoH,EAAO0C,MACfV,EACAQ,EAASmM,aACTnM,EAAS0E,4BAIb,OADA8H,EAAoBpW,GACbiC,CAAM,EAoBTkU,EAA2B/H,MAC/BjE,EACA4N,EACAF,EAEI,CACFG,OAAO,MAGT,IAAK,MAAMhY,KAAQmK,EAAQ,CACzB,MAAMpB,EAAQoB,EAAOnK,GAErB,GAAI+I,EAAO,CACT,MAAMM,GAAEA,KAAOsN,GAAe5N,EAE9B,GAAIM,EAAI,CACN,MAAM4O,EAAmB7Q,EAAOiB,MAAMpI,IAAIoJ,EAAGrJ,MACvCkY,EACJnP,EAAMM,QFzcF8O,EEyc8BpP,EAAgBM,OFvc1D8O,EAAerJ,aAEdhC,GAAWqL,EAAerJ,WACzBqJ,EAAerJ,SAAS5N,YAAYlB,OAAS4U,IAC9CrV,EAAS4Y,EAAerJ,WACvB7K,OAAO4D,OAAOsQ,EAAerJ,UAAUpK,MACpC0T,GACCA,EAAiBlX,YAAYlB,OAAS4U,OEkclCsD,GAAqB9T,EAAgBoC,kBACvC4P,EAAoB,CAACpW,IAAO,GAG9B,MAAMqY,QAAmBlK,GACvBpF,EACA3B,EAAO3B,SACPsC,EACA+N,EACAlM,EAAS0E,4BAA8ByJ,EACvCE,GAOF,GAJIC,GAAqB9T,EAAgBoC,kBACvC4P,EAAoB,CAACpW,IAGnBqY,EAAWhP,EAAGrJ,QAChB6X,EAAQG,OAAQ,EACZD,GACF,OAIHA,IACElW,EAAIwW,EAAYhP,EAAGrJ,MAChBiY,EACEtL,EACE1G,EAAWU,OACX0R,EACAhP,EAAGrJ,MAEL0C,EAAIuD,EAAWU,OAAQ0C,EAAGrJ,KAAMqY,EAAWhP,EAAGrJ,OAChDkS,GAAMjM,EAAWU,OAAQ0C,EAAGrJ,MACnC,EAEAqE,EAAcsS,UACNR,EACLQ,EACAoB,EACAF,EAEL,CACF,CFtfU,IAACM,EEwfZ,OAAON,EAAQG,KAAK,EAiBhBf,EAAwB,CAACjX,EAAMU,KAClCkJ,EAASnE,WACTzF,GAAQU,GAAQgC,EAAIqF,EAAa/H,EAAMU,IACvCuS,GAAUqF,KAAatU,IAEpBgE,EAAyC,CAC7CjI,EACAiC,EACAsF,IAEAH,EACEpH,EACAqH,EACA,IACM2C,EAAOD,MACP/B,EACArG,EAAYM,GACVgC,EACAkD,EAASnH,GACP,CAAEA,CAACA,GAAQiC,GACXA,GAEVsF,EACAtF,GAcEyU,EAAgB,CACpBzW,EACAb,EACAoM,EAA0B,CAAA,KAE1B,MAAMxC,EAAelH,EAAIuH,EAASpJ,GAClC,IAAI2W,EAAsBxX,EAE1B,GAAI4J,EAAO,CACT,MAAMoP,EAAiBpP,EAAMM,GAEzB8O,KACDA,EAAe1S,UACd/C,EAAIqF,EAAa/H,EAAMmU,GAAgBhV,EAAOgZ,IAEhDxB,EACE5J,GAAcoL,EAAejP,MAAQ7J,EAAkBF,GACnD,GACAA,EAEFuU,GAAiByE,EAAejP,KAClC,IAAIiP,EAAejP,IAAIqC,SAAS8K,SAC7BkC,GACEA,EAAUC,SACT7B,EACAnD,SAAS+E,EAAUpZ,SAEhBgZ,EAAezL,KACpB3N,EAAgBoZ,EAAejP,KACjCiP,EAAezL,KAAK7J,OAAS,EACzBsV,EAAezL,KAAK2J,SACjBoC,KACGA,EAAYjC,iBAAmBiC,EAAYhT,YAC5CgT,EAAY5Y,QAAUL,MAAMC,QAAQkX,KAC9BA,EAAkBjS,MAClBhE,GAAiBA,IAAS+X,EAAYtZ,QAEzCwX,IAAe8B,EAAYtZ,SAEnCgZ,EAAezL,KAAK,KACnByL,EAAezL,KAAK,GAAG7M,UAAY8W,GAExCwB,EAAezL,KAAK2J,SACjBqC,GACEA,EAAS7Y,QAAU6Y,EAASvZ,QAAUwX,IAGpC9J,GAAYsL,EAAejP,KACpCiP,EAAejP,IAAI/J,MAAQ,IAE3BgZ,EAAejP,IAAI/J,MAAQwX,EAEtBwB,EAAejP,IAAIjK,MACtB6H,EAAUe,OAAOjC,KAAK,CACpB5F,OACA6H,OAAQ,IAAKE,MAKtB,EAEAwD,EAAQqL,aAAerL,EAAQoN,cAC9BjC,EACE1W,EACA2W,EACApL,EAAQoN,YACRpN,EAAQqL,aACR,GAGJrL,EAAQqN,gBAAkBC,EAAQ7Y,EAA2B,EAGzD8Y,EAAY,CAKhB9Y,EACAb,EACAoM,KAEA,IAAK,MAAMwN,KAAY5Z,EAAO,CAC5B,MAAMwX,EAAaxX,EAAM4Z,GACnBrR,EAAY,GAAG1H,KAAQ+Y,IACvBhQ,EAAQlH,EAAIuH,EAAS1B,IAE1BN,EAAOiB,MAAMpI,IAAID,IAChBT,EAASoX,IACR5N,IAAUA,EAAMM,MAClBnK,EAAayX,GACVmC,EAAUpR,EAAWiP,EAAYpL,GACjCkL,EAAc/O,EAAWiP,EAAYpL,EAC1C,GAGGyN,EAA0C,CAC9ChZ,EACAb,EACAoM,EAAU,CAAA,KAEV,MAAMxC,EAAQlH,EAAIuH,EAASpJ,GACrBuO,EAAenH,EAAOiB,MAAMpI,IAAID,GAChCiZ,EAAaxY,EAAYtB,GAE/BuD,EAAIqF,EAAa/H,EAAMiZ,GAEnB1K,GACFzH,EAAUuB,MAAMzC,KAAK,CACnB5F,OACA6H,OAAQ,IAAKE,MAIZ3D,EAAgBgC,SAAWhC,EAAgBkC,cAC5CiF,EAAQqL,aAER9P,EAAUC,MAAMnB,KAAK,CACnB5F,OACAsG,YAAa4N,GAAelQ,EAAgB+D,GAC5C3B,QAAS6Q,EAAUjX,EAAMiZ,OAI7BlQ,GAAUA,EAAMM,IAAOhK,EAAkB4Z,GAErCxC,EAAczW,EAAMiZ,EAAY1N,GADhCuN,EAAU9Y,EAAMiZ,EAAY1N,GAIlCW,EAAUlM,EAAMoH,IAAWN,EAAUC,MAAMnB,KAAK,IAAKK,IACrDa,EAAUe,OAAOjC,KAAK,CACpB5F,KAAM+J,EAAOD,MAAQ9J,OAAO4B,EAC5BiG,OAAQ,IAAKE,IACb,EAGEiB,EAA0BoF,MAAOzO,IACrCoK,EAAOD,OAAQ,EACf,MAAMlK,EAASD,EAAMC,OACrB,IAAII,EAAOJ,EAAOI,KACdkZ,GAAsB,EAC1B,MAAMnQ,EAAelH,EAAIuH,EAASpJ,GAG5BmZ,EAA8BxC,IAClCuC,EACEE,OAAOnW,MAAM0T,IACZzX,EAAayX,IAAe1T,MAAM0T,EAAWvD,YAC9CH,GAAU0D,EAAY9U,EAAIkG,EAAa/H,EAAM2W,GAAY,EAG7D,GAAI5N,EAAO,CACT,IAAID,EACApC,EACJ,MAAMiQ,EAXN/W,EAAOX,KAAOqV,GAAcvL,EAAMM,IAAM3J,EAAcC,GAYhDwM,EACJxM,EAAMV,OAASiE,GAAevD,EAAMV,OAASiE,EACzCmW,KChtBI9N,EDitBQxC,EAAMM,IChtBpBS,QACPyB,EAAQiD,UACPjD,EAAQoD,KACRpD,EAAQqD,KACRrD,EAAQkD,WACRlD,EAAQmD,WACRnD,EAAQsD,SACRtD,EAAQuD,WD0sBDlF,EAASqM,UACTpU,EAAIoE,EAAWU,OAAQ3G,IACvB+I,EAAMM,GAAGiQ,OEptBL,EACbnN,EACAtD,EACA0M,EACAL,EAIAtJ,KAEIA,EAAKI,WAEGuJ,GAAe3J,EAAKK,YACrBpD,GAAasD,IACboJ,EAAcL,EAAepJ,SAAWF,EAAKE,WAC9CK,IACCoJ,EAAcL,EAAenJ,WAAaH,EAAKG,aACjDI,GFosBHoN,CACEpN,EACAtK,EAAIoE,EAAWM,cAAevG,GAC9BiG,EAAWsP,YACXM,EACAD,GAEE4D,EAAUtN,EAAUlM,EAAMoH,EAAQ+E,GAExCzJ,EAAIqF,EAAa/H,EAAM2W,GAEnBxK,GACFpD,EAAMM,GAAGJ,QAAUF,EAAMM,GAAGJ,OAAOtJ,GACnC0V,GAAsBA,EAAmB,IAChCtM,EAAMM,GAAGL,UAClBD,EAAMM,GAAGL,SAASrJ,GAGpB,MAAM8I,EAAaiO,EACjB1W,EACA2W,EACAxK,GACA,GAGI0K,GAAgBxS,EAAcoE,IAAe+Q,EASnD,IAPCrN,GACCrF,EAAUe,OAAOjC,KAAK,CACpB5F,OACAf,KAAMU,EAAMV,KACZ4I,OAAQ,IAAKE,KAGbsR,EASF,OARIjV,EAAgBsC,UACI,WAAlBkD,EAASgC,MAAqBO,EAChCnF,IACUmF,GACVnF,KAKF6P,GACA/P,EAAUC,MAAMnB,KAAK,CAAE5F,UAAUwZ,EAAU,CAAE,EAAG/Q,IAMpD,IAFC0D,GAAeqN,GAAW1S,EAAUC,MAAMnB,KAAK,IAAKK,IAEjD2D,EAASqM,SAAU,CACrB,MAAMtP,OAAEA,SAAiBuP,EAAe,CAAClW,IAIzC,GAFAmZ,EAA2BxC,GAEvBuC,EAAqB,CACvB,MAAMO,EAA4B5E,GAChC5O,EAAWU,OACXyC,EACApJ,GAEI0Z,EAAoB7E,GACxBlO,EACAyC,EACAqQ,EAA0BzZ,MAAQA,GAGpC8I,EAAQ4Q,EAAkB5Q,MAC1B9I,EAAO0Z,EAAkB1Z,KAEzB0G,EAAUrC,EAAcsC,EACzB,CACF,MACCyP,EAAoB,CAACpW,IAAO,GAC5B8I,SACQqF,GACJpF,EACA3B,EAAO3B,SACPsC,EACA+N,EACAlM,EAAS0E,4BAEXtO,GACFoW,EAAoB,CAACpW,IAErBmZ,EAA2BxC,GAEvBuC,IACEpQ,EACFpC,GAAU,EACDtC,EAAgBsC,UACzBA,QAAgByP,EAAyB/M,GAAS,KAKpD8P,IACFnQ,EAAMM,GAAGiQ,MACPT,EACE9P,EAAMM,GAAGiQ,MAIblC,EAAoBpX,EAAM0G,EAASoC,EAAOL,GAE7C,CC/zBU,IAAC8C,CD+zBX,EAGGoO,EAAc,CAACzQ,EAAU5H,KAC7B,GAAIO,EAAIoE,EAAWU,OAAQrF,IAAQ4H,EAAII,MAErC,OADAJ,EAAII,QACG,CAEF,EAGHuP,EAAwCzK,MAAOpO,EAAMuL,EAAU,CAAA,KACnE,IAAI7E,EACAoK,EACJ,MAAM8I,EAAajV,EAAsB3E,GAEzC,GAAI4J,EAASqM,SAAU,CACrB,MAAMtP,OAta0ByH,OAAOrO,IACzC,MAAM4G,OAAEA,SAAiBuP,EAAenW,GAExC,GAAIA,EACF,IAAK,MAAMC,KAAQD,EAAO,CACxB,MAAM+I,EAAQjH,EAAI8E,EAAQ3G,GAC1B8I,EACIpG,EAAIuD,EAAWU,OAAQ3G,EAAM8I,GAC7BoJ,GAAMjM,EAAWU,OAAQ3G,EAC9B,MAEDiG,EAAWU,OAASA,EAGtB,OAAOA,CAAM,EAwZUkT,CACnBnY,EAAY1B,GAAQA,EAAO4Z,GAG7BlT,EAAUrC,EAAcsC,GACxBmK,EAAmB9Q,GACd4Z,EAAW7U,MAAM/E,GAAS6B,EAAI8E,EAAQ3G,KACvC0G,CACL,MAAU1G,GACT8Q,SACQgJ,QAAQC,IACZH,EAAWnS,KAAI2G,MAAO1G,IACpB,MAAMqB,EAAQlH,EAAIuH,EAAS1B,GAC3B,aAAayO,EACXpN,GAASA,EAAMM,GAAK,CAAE3B,CAACA,GAAYqB,GAAUA,EAC9C,MAGLiF,MAAMvM,UACLqP,GAAqB7K,EAAWS,UAAYM,KAE/C8J,EAAmBpK,QAAgByP,EAAyB/M,GAoB9D,OAjBAtC,EAAUC,MAAMnB,KAAK,KACdsB,EAASlH,IACboE,EAAgBsC,SAAWA,IAAYT,EAAWS,QAC/C,CAAE,EACF,CAAE1G,WACF4J,EAASqM,WAAajW,EAAO,CAAE0G,WAAY,GAC/CC,OAAQV,EAAWU,SAGrB4E,EAAQC,cACLsF,GACDxE,EACElD,EACAuQ,EACA3Z,EAAO4Z,EAAaxS,EAAO0C,OAGxBgH,CAAgB,EAGnBwH,GACJsB,IAIA,MAAM/R,EAAS,IACTkC,EAAOD,MAAQ/B,EAAc/D,GAGnC,OAAOtC,EAAYkY,GACf/R,EACAX,EAAS0S,GACP/X,EAAIgG,EAAQ+R,GACZA,EAAWnS,KAAKzH,GAAS6B,EAAIgG,EAAQ7H,IAAM,EAG7Cga,GAAoD,CACxDha,EACA2D,KACI,CACJgF,UAAW9G,GAAK8B,GAAasC,GAAYU,OAAQ3G,GACjDoG,UAAWvE,GAAK8B,GAAasC,GAAYK,YAAatG,GACtD8I,MAAOjH,GAAK8B,GAAasC,GAAYU,OAAQ3G,GAC7CyG,eAAgB5E,EAAIoE,EAAWO,iBAAkBxG,GACjD6I,YAAahH,GAAK8B,GAAasC,GAAYM,cAAevG,KActDia,GAA0C,CAACja,EAAM8I,EAAOyC,KAC5D,MAAMrC,GAAOrH,EAAIuH,EAASpJ,EAAM,CAAEqJ,GAAI,CAAA,IAAMA,IAAM,CAAA,GAAIH,IAChDgR,EAAerY,EAAIoE,EAAWU,OAAQ3G,IAAS,IAG7CkJ,IAAKiR,EAAU1Q,QAAEA,EAAOxK,KAAEA,KAASmb,GAAoBF,EAE/DxX,EAAIuD,EAAWU,OAAQ3G,EAAM,IACxBoa,KACAtR,EACHI,QAGFpC,EAAUC,MAAMnB,KAAK,CACnB5F,OACA2G,OAAQV,EAAWU,OACnBD,SAAS,IAGX6E,GAAWA,EAAQC,aAAetC,GAAOA,EAAII,OAASJ,EAAII,OAAO,EA4B7DW,GAA8C,CAACjK,EAAMuL,EAAU,CAAA,KACnE,IAAK,MAAM7D,KAAa1H,EAAO2E,EAAsB3E,GAAQoH,EAAO0C,MAClE1C,EAAO0C,MAAMuQ,OAAO3S,GACpBN,EAAOiB,MAAMgS,OAAO3S,GAEf6D,EAAQ+O,YACXpI,GAAM9I,EAAS1B,GACfwK,GAAMnK,EAAaL,KAGpB6D,EAAQgP,WAAarI,GAAMjM,EAAWU,OAAQe,IAC9C6D,EAAQiP,WAAatI,GAAMjM,EAAWK,YAAaoB,IACnD6D,EAAQkP,aAAevI,GAAMjM,EAAWM,cAAemB,IACvD6D,EAAQmP,kBACPxI,GAAMjM,EAAWO,iBAAkBkB,IACpCkC,EAASzB,mBACPoD,EAAQoP,kBACTzI,GAAMlO,EAAgB0D,GAG1BZ,EAAUe,OAAOjC,KAAK,CACpBiC,OAAQ,IAAKE,KAGfjB,EAAUC,MAAMnB,KAAK,IAChBK,KACEsF,EAAQiP,UAAiB,CAAEpU,QAAS6Q,KAAhB,CAAA,KAG1B1L,EAAQqP,aAAe5T,GAAc,EAGlCkD,GAAsE,EAC1EzE,WACAzF,OACA+I,QACAoB,cAGG/H,EAAUqD,IAAasE,EAAOD,OAC7BrE,GACF2B,EAAO3B,SAASxF,IAAID,MAEpByF,EAAW2B,EAAO3B,SAAS+B,IAAIxH,GAAQoH,EAAO3B,SAAS4U,OAAOra,GAE9D0W,EACE1W,EACAsU,GAAcvL,EAAQA,EAAMM,GAAKxH,EAAIsI,EAAQnK,GAAMqJ,KACnD,GACA,GACA,GAEH,EAGGd,GAA0C,CAACvI,EAAMuL,EAAU,CAAA,KAC/D,IAAIxC,EAAQlH,EAAIuH,EAASpJ,GACzB,MAAM6a,EACJzY,EAAUmJ,EAAQ9F,WAAarD,EAAUwH,EAASnE,UAyBpD,OAvBA/C,EAAI0G,EAASpJ,EAAM,IACb+I,GAAS,CAAA,EACbM,GAAI,IACEN,GAASA,EAAMM,GAAKN,EAAMM,GAAK,CAAEH,IAAK,CAAElJ,SAC5CA,OACA8J,OAAO,KACJyB,KAGPnE,EAAO0C,MAAMtC,IAAIxH,GAEb+I,EACFmB,GAAqB,CACnBnB,QACAtD,SAAUrD,EAAUmJ,EAAQ9F,UACxB8F,EAAQ9F,SACRmE,EAASnE,SACbzF,SAGFsW,EAAoBtW,GAAM,EAAMuL,EAAQpM,OAGnC,IACD0b,EACA,CAAEpV,SAAU8F,EAAQ9F,UAAYmE,EAASnE,UACzC,MACAmE,EAASkR,YACT,CACEtM,WAAYjD,EAAQiD,SACpBG,IAAK8F,GAAalJ,EAAQoD,KAC1BC,IAAK6F,GAAalJ,EAAQqD,KAC1BF,UAAW+F,GAAqBlJ,EAAQmD,WACxCD,UAAWgG,GAAalJ,EAAQkD,WAChCI,QAAS4F,GAAalJ,EAAQsD,UAEhC,GACJ7O,OACAgJ,WACAC,OAAQD,EACRE,IAAMA,IACJ,GAAIA,EAAK,CACPX,GAASvI,EAAMuL,GACfxC,EAAQlH,EAAIuH,EAASpJ,GAErB,MAAM+a,EAAWrZ,EAAYwH,EAAI/J,QAC7B+J,EAAI8R,kBACD9R,EAAI8R,iBAAiB,yBAAyB,IAEjD9R,EACE+R,EG5jCD,CAAC/R,GACdkE,GAAalE,IAAQnK,EAAgBmK,GH2jCLkG,CAAkB2L,GACpCrO,EAAO3D,EAAMM,GAAGqD,MAAQ,GAE9B,GACEuO,EACIvO,EAAKhI,MAAMgJ,GAAgBA,IAAWqN,IACtCA,IAAahS,EAAMM,GAAGH,IAE1B,OAGFxG,EAAI0G,EAASpJ,EAAM,CACjBqJ,GAAI,IACCN,EAAMM,MACL4R,EACA,CACEvO,KAAM,IACDA,EAAKlL,OAAOmS,IACfoH,KACIvb,MAAMC,QAAQoC,EAAImC,EAAgBhE,IAAS,CAAC,IAAM,IAExDkJ,IAAK,CAAEjK,KAAM8b,EAAS9b,KAAMe,SAE9B,CAAEkJ,IAAK6R,MAIfzE,EAAoBtW,GAAM,OAAO4B,EAAWmZ,EAC7C,MACChS,EAAQlH,EAAIuH,EAASpJ,EAAM,CAAE,GAEzB+I,EAAMM,KACRN,EAAMM,GAAGS,OAAQ,IAGlBF,EAASzB,kBAAoBoD,EAAQpD,qBAClCrI,EAAmBsH,EAAOiB,MAAOrI,KAAS+J,EAAOC,SACnD5C,EAAOsO,QAAQlO,IAAIxH,EACtB,EAEJ,EAGGkb,GAAc,IAClBtR,EAASuL,kBACT7I,EAAsBlD,EAASuQ,EAAavS,EAAO0C,OAyB/CqR,GACJ,CAACC,EAASC,IAAcjN,MAAOkN,IAC7B,IAAIC,EACAD,IACFA,EAAEE,gBAAkBF,EAAEE,iBACtBF,EAAEG,SAAWH,EAAEG,WAGjB,IAAIhJ,EAAchS,EAAYsH,GAE9B,GAAIX,EAAO3B,SAASiW,KAClB,IAAK,MAAM1b,KAAQoH,EAAO3B,SACxB/C,EAAI+P,EAAazS,OAAM4B,GAQ3B,GAJAkF,EAAUC,MAAMnB,KAAK,CACnB4P,cAAc,IAGZ5L,EAASqM,SAAU,CACrB,MAAMtP,OAAEA,EAAMkB,OAAEA,SAAiBqO,IACjCjQ,EAAWU,OAASA,EACpB8L,EAAc5K,CACf,YACOsO,EAAyB/M,GAKjC,GAFA8I,GAAMjM,EAAWU,OAAQ,QAErBtC,EAAc4B,EAAWU,QAAS,CACpCG,EAAUC,MAAMnB,KAAK,CACnBe,OAAQ,CAAE,IAEZ,UACQyU,EAAQ3I,EAA6B6I,EAC5C,CAAC,MAAOxS,GACPyS,EAAezS,CAChB,CACF,MACKuS,SACIA,EAAU,IAAKpV,EAAWU,QAAU2U,GAE5CJ,KACAvD,WAAWuD,IAUb,GAPApU,EAAUC,MAAMnB,KAAK,CACnB2P,aAAa,EACbC,cAAc,EACdC,mBAAoBpR,EAAc4B,EAAWU,UAAY4U,EACzDjG,YAAarP,EAAWqP,YAAc,EACtC3O,OAAQV,EAAWU,SAEjB4U,EACF,MAAMA,CACP,EAsCCI,GAAqC,CACzCtU,EACAuU,EAAmB,CAAA,KAEnB,MAAMC,EAAgBxU,EAAa5G,EAAY4G,GAAcrD,EACvD8X,EAAqBrb,EAAYob,GACjCE,EAAqB1X,EAAcgD,GACnCQ,EAASkU,EAAqB/X,EAAiB8X,EAMrD,GAJKF,EAAiBI,oBACpBhY,EAAiB6X,IAGdD,EAAiBK,WAAY,CAChC,GAAIL,EAAiBM,gBAAiB,CACpC,MAAMC,EAAgB,IAAIrb,IAAI,IACzBsG,EAAO0C,SACP7F,OAAOK,KAAK4P,GAAelQ,EAAgB+D,MAEhD,IAAK,MAAML,KAAalI,MAAM2R,KAAKgL,GACjCta,EAAIoE,EAAWK,YAAaoB,GACxBhF,EAAImF,EAAQH,EAAW7F,EAAIkG,EAAaL,IACxCsR,EACEtR,EACA7F,EAAIgG,EAAQH,GAGrB,KAAM,CACL,GAAIrH,GAASqB,EAAY2F,GACvB,IAAK,MAAMrH,KAAQoH,EAAO0C,MAAO,CAC/B,MAAMf,EAAQlH,EAAIuH,EAASpJ,GAC3B,GAAI+I,GAASA,EAAMM,GAAI,CACrB,MAAM8O,EAAiB3Y,MAAMC,QAAQsJ,EAAMM,GAAGqD,MAC1C3D,EAAMM,GAAGqD,KAAK,GACd3D,EAAMM,GAAGH,IAEb,GAAI6D,GAAcoL,GAAiB,CACjC,MAAMiE,EAAOjE,EAAekE,QAAQ,QACpC,GAAID,EAAM,CACRA,EAAKE,QACL,KACD,CACF,CACF,CACF,CAGHlT,EAAU,CAAA,CACX,CAEDrB,EAAc6B,EAASzB,iBACnByT,EAAiBI,kBACfvb,EAAYuD,GACZ,CAAE,EACJvD,EAAYoH,GAEhBf,EAAUuB,MAAMzC,KAAK,CACnBiC,OAAQ,IAAKA,KAGff,EAAUe,OAAOjC,KAAK,CACpBiC,OAAQ,IAAKA,IAEhB,CAEDT,EAAS,CACP0C,MAAO8R,EAAiBM,gBAAkB9U,EAAO0C,MAAQ,IAAIhJ,IAC7D4U,QAAS,IAAI5U,IACbuH,MAAO,IAAIvH,IACX2E,SAAU,IAAI3E,IACdyG,MAAO,IAAIzG,IACX6G,UAAU,EACV2B,MAAO,IAGTS,EAAOD,OACJ1F,EAAgBsC,WACfkV,EAAiBhB,eACjBgB,EAAiBM,gBAErBnS,EAAOxC,QAAUqC,EAASzB,iBAE1BrB,EAAUC,MAAMnB,KAAK,CACnB0P,YAAasG,EAAiBW,gBAC1BtW,EAAWqP,YACX,EACJlP,SAAS2V,IAELH,EAAiBpB,UACfvU,EAAWG,WAETwV,EAAiBI,mBAChB/I,GAAU5L,EAAYrD,KAE/BuR,cAAaqG,EAAiBY,iBAC1BvW,EAAWsP,YAEfjP,YAAayV,EACT,CAAE,EACFH,EAAiBM,gBACfN,EAAiBI,mBAAqBjU,EACpCmM,GAAelQ,EAAgB+D,GAC/B9B,EAAWK,YACbsV,EAAiBI,mBAAqB3U,EACpC6M,GAAelQ,EAAgBqD,GAC/BuU,EAAiBpB,UACfvU,EAAWK,YACX,CAAE,EACZC,cAAeqV,EAAiBnB,YAC5BxU,EAAWM,cACX,CAAE,EACNI,OAAQiV,EAAiBa,WAAaxW,EAAWU,OAAS,CAAE,EAC5D8O,qBAAoBmG,EAAiBc,wBACjCzW,EAAWwP,mBAEfD,cAAc,GACd,EAGE8G,GAAoC,CAACjV,EAAYuU,IACrDD,GACE7O,GAAWzF,GACNA,EAAwBU,GACzBV,EACJuU,GAuCJ,MAAO,CACLhY,QAAS,CACP2E,YACA0B,cACA+P,iBACAmB,gBACAlB,YACA/D,iBACAlO,YACAiP,YACAjQ,eACAiB,iBAl5BqB,KACvB,IAAK,MAAMjI,KAAQoH,EAAOsO,QAAS,CACjC,MAAM3M,EAAelH,EAAIuH,EAASpJ,GAElC+I,IACGA,EAAMM,GAAGqD,KACN3D,EAAMM,GAAGqD,KAAKsB,OAAO9E,IAASyK,GAAKzK,MAClCyK,GAAK5K,EAAMM,GAAGH,OACnBe,GAAWjK,EACd,CAEDoH,EAAOsO,QAAU,IAAI5U,GAAK,EAw4BxB6b,kBA5sC6C,CAC/C3c,EACA6H,EAAS,GACT+U,EACAC,EACAC,GAAkB,EAClBC,GAA6B,KAE7B,GAAIF,GAAQD,IAAWhT,EAASnE,SAAU,CAExC,GADAsE,EAAOC,QAAS,EACZ+S,GAA8Bvd,MAAMC,QAAQoC,EAAIuH,EAASpJ,IAAQ,CACnE,MAAMyS,EAAcmK,EAAO/a,EAAIuH,EAASpJ,GAAO6c,EAAKG,KAAMH,EAAKI,MAC/DH,GAAmBpa,EAAI0G,EAASpJ,EAAMyS,EACvC,CAED,GACEsK,GACAvd,MAAMC,QAAQoC,EAAIoE,EAAWU,OAAQ3G,IACrC,CACA,MAAM2G,EAASiW,EACb/a,EAAIoE,EAAWU,OAAQ3G,GACvB6c,EAAKG,KACLH,EAAKI,MAEPH,GAAmBpa,EAAIuD,EAAWU,OAAQ3G,EAAM2G,GI5NzC,EAAIuC,EAAQlJ,MACxBuB,EAAQM,EAAIqH,EAAKlJ,IAAO6C,QAAUqP,GAAMhJ,EAAKlJ,EAAK,EJ4N7Ckd,CAAgBjX,EAAWU,OAAQ3G,EACpC,CAED,GACEoE,EAAgBmC,eAChBwW,GACAvd,MAAMC,QAAQoC,EAAIoE,EAAWM,cAAevG,IAC5C,CACA,MAAMuG,EAAgBqW,EACpB/a,EAAIoE,EAAWM,cAAevG,GAC9B6c,EAAKG,KACLH,EAAKI,MAEPH,GAAmBpa,EAAIuD,EAAWM,cAAevG,EAAMuG,EACxD,CAEGnC,EAAgBkC,cAClBL,EAAWK,YAAc4N,GAAelQ,EAAgB+D,IAG1DjB,EAAUC,MAAMnB,KAAK,CACnB5F,OACAoG,QAAS6Q,EAAUjX,EAAM6H,GACzBvB,YAAaL,EAAWK,YACxBK,OAAQV,EAAWU,OACnBD,QAAST,EAAWS,SAEvB,MACChE,EAAIqF,EAAa/H,EAAM6H,EACxB,EAupCCqC,wBACAiT,eA52BFnd,GAEAuB,EACEM,EACEkI,EAAOD,MAAQ/B,EAAc/D,EAC7BhE,EACA4J,EAASzB,iBAAmBtG,EAAImC,EAAgBhE,EAAM,IAAM,KAu2B9D2b,UACAyB,oBAzBwB,IAC1BtQ,GAAWlD,EAAS7F,gBACnB6F,EAAS7F,gBAA6BsZ,MAAMxV,IAC3CyU,GAAMzU,EAAQ+B,EAAS0T,cACvBxW,EAAUC,MAAMnB,KAAK,CACnBS,WAAW,GACX,IAoBFQ,iBAlCF+Q,IAEA3R,EAAa,IACRA,KACA2R,EACJ,EA8BC2F,aA1SkB9X,IAChBrD,EAAUqD,KACZqB,EAAUC,MAAMnB,KAAK,CAAEH,aACvB6G,EACElD,GACA,CAACF,EAAKlJ,KACJ,MAAMyM,EAAsB5K,EAAIuH,EAASpJ,GACrCyM,IACFvD,EAAIzD,SAAWgH,EAAapD,GAAG5D,UAAYA,EAEvCjG,MAAMC,QAAQgN,EAAapD,GAAGqD,OAChCD,EAAapD,GAAGqD,KAAK2J,SAASpH,IAC5BA,EAASxJ,SAAWgH,EAAapD,GAAG5D,UAAYA,CAAQ,IAG7D,GAEH,GACA,GAEH,EAuRCqB,YACA1C,kBACAoZ,WArpCgB7W,IAClBV,EAAWU,OAASA,EACpBG,EAAUC,MAAMnB,KAAK,CACnBe,OAAQV,EAAWU,OACnBD,SAAS,GACT,EAipCA,WAAI0C,GACF,OAAOA,CACR,EACD,eAAIrB,GACF,OAAOA,CACR,EACD,UAAIgC,GACF,OAAOA,CACR,EACD,UAAIA,CAAO5K,GACT4K,EAAS5K,CACV,EACD,kBAAI6E,GACF,OAAOA,CACR,EACD,UAAIoD,GACF,OAAOA,CACR,EACD,UAAIA,CAAOjI,GACTiI,EAASjI,CACV,EACD,cAAI8G,GACF,OAAOA,CACR,EACD,cAAIA,CAAW9G,GACb8G,EAAa9G,CACd,EACD,YAAIyK,GACF,OAAOA,CACR,EACD,YAAIA,CAASzK,GACXyK,EAAW,IACNA,KACAzK,EAEN,GAEH0Z,UACAtQ,YACA4S,gBACA5T,MA5gBwC,CACxCvH,EAIAgC,IAEA8K,GAAW9M,GACP8G,EAAUe,OAAOlC,UAAU,CACzBC,KAAO6X,GACLzd,EACEgI,OAAUpG,EAAWI,GACrByb,KAONzV,EACEhI,EACAgC,GACA,GAufNgX,WACAV,aACAgE,SACAoB,WAxQkD,CAAC1d,EAAMuL,EAAU,CAAA,KAC/D1J,EAAIuH,EAASpJ,KACX0B,EAAY6J,EAAQvJ,cACtBgX,EAAShZ,EAAMS,EAAYoB,EAAImC,EAAgBhE,MAE/CgZ,EACEhZ,EACAuL,EAAQvJ,cAKVU,EAAIsB,EAAgBhE,EAAMS,EAAY8K,EAAQvJ,gBAG3CuJ,EAAQkP,aACXvI,GAAMjM,EAAWM,cAAevG,GAG7BuL,EAAQiP,YACXtI,GAAMjM,EAAWK,YAAatG,GAC9BiG,EAAWG,QAAUmF,EAAQvJ,aACzBiV,EAAUjX,EAAMS,EAAYoB,EAAImC,EAAgBhE,KAChDiX,KAGD1L,EAAQgP,YACXrI,GAAMjM,EAAWU,OAAQ3G,GACzBoE,EAAgBsC,SAAWM,KAG7BF,EAAUC,MAAMnB,KAAK,IAAKK,IAC3B,EAyOD0X,YAljBqD3d,IACrDA,GACE2E,EAAsB3E,GAAMqW,SAASuH,GACnC1L,GAAMjM,EAAWU,OAAQiX,KAG7B9W,EAAUC,MAAMnB,KAAK,CACnBe,OAAQ3G,EAAOiG,EAAWU,OAAS,CAAE,GACrC,EA2iBFsD,cACAgQ,YACA4D,SA1G8C,CAAC7d,EAAMuL,EAAU,CAAA,KAC/D,MAAMxC,EAAQlH,EAAIuH,EAASpJ,GACrBmY,EAAiBpP,GAASA,EAAMM,GAEtC,GAAI8O,EAAgB,CAClB,MAAM4C,EAAW5C,EAAezL,KAC5ByL,EAAezL,KAAK,GACpByL,EAAejP,IAEf6R,EAASzR,QACXyR,EAASzR,QACTiC,EAAQuS,cACNhR,GAAWiO,EAASxR,SACpBwR,EAASxR,SAEd,GA4FDyQ,iBAEJ,oB3Ch6CE7U,GACGA,EAAM4Y,OAAO7V,EAAmC/C,iBEpBrD,SAGEA,GACA,MAAMY,EAAUvC,KACTwa,EAASC,GAAc3a,EAAM0C,UAAS,IACvCpC,QACJA,EAAUmC,EAAQnC,QAAOsa,SACzBA,EAAQC,SACRA,EAAQnU,OACRA,EAAM4S,OACNA,EAASnS,EAAY2T,QACrBA,EAAOC,QACPA,EAAOC,QACPA,EAAOP,OACPA,EAAMQ,UACNA,EAASC,eACTA,KACGC,GACDtZ,EAEEuZ,EAAStQ,MAAOzO,IACpB,IAAIgf,GAAW,EACX1f,EAAO,SAEL2E,EAAQuX,cAAa/M,MAAO1N,IAChC,MAAMke,EAAW,IAAIC,SACrB,IAAIC,EAAe,GAEnB,IACEA,EAAeC,KAAKC,UAAUte,EAC/B,CAAC,MAAMue,GAAE,CAEV,MAAMC,EAAoB9U,EAAQxG,EAAQmE,aAE1C,IAAK,MAAMzG,KAAO4d,EAChBN,EAASO,OAAO7d,EAAK4d,EAAkB5d,IAazC,GAVI4c,SACIA,EAAS,CACbxd,OACAf,QACAid,SACAgC,WACAE,iBAIA9U,EACF,IACE,MAAMoV,EAAgC,CACpChB,GAAWA,EAAQ,gBACnBC,GACAtZ,MAAM5F,GAAUA,GAASA,EAAMqU,SAAS,UAEpC6L,QAAiBC,MAAMC,OAAOvV,GAAS,CAC3C4S,SACAwB,QAAS,IACJA,KACCC,EAAU,CAAE,eAAgBA,GAAY,CAAA,GAE9CmB,KAAMJ,EAAgCN,EAAeF,IAIrDS,IACCb,GACIA,EAAea,EAASI,QACzBJ,EAASI,OAAS,KAAOJ,EAASI,QAAU,MAEhDd,GAAW,EACXL,GAAWA,EAAQ,CAAEe,aACrBpgB,EAAOsgB,OAAOF,EAASI,SAEvBlB,GAAaA,EAAU,CAAEc,YAE5B,CAAC,MAAOvW,GACP6V,GAAW,EACXL,GAAWA,EAAQ,CAAExV,SACtB,CACF,GAxDGlF,CAyDHjE,GAECgf,GAAYxZ,EAAMvB,UACpBuB,EAAMvB,QAAQkD,UAAUC,MAAMnB,KAAK,CACjC6P,oBAAoB,IAEtBtQ,EAAMvB,QAAQqW,SAAS,cAAe,CACpChb,SAEH,EAOH,OAJAqE,EAAMiC,WAAU,KACd0Y,GAAW,EAAK,GACf,IAEIF,EACLza,EAAAoc,cAAApc,EAAAqc,SAAA,KACG5B,EAAO,CACNW,YAIJpb,EAAAoc,cAAA,OAAA,CACEE,WAAY5B,EACZhU,OAAQA,EACR4S,OAAQA,EACRyB,QAASA,EACTH,SAAUQ,KACND,GAEHN,EAGP,uBdhEEhZ,IAEA,MAAMgZ,SAAEA,KAAazd,GAASyE,EAC9B,OACE7B,EAAAoc,cAACrc,EAAgBwc,SAAQ,CAAC1gB,MAAOuB,GAC9Byd,EAEH,mG4DTE,SAMJhZ,GAEA,MAAMY,EAAUvC,KACVI,QACJA,EAAUmC,EAAQnC,QAAO5D,KACzBA,EAAI8f,QACJA,EAAU,KAAI3X,iBACdA,EAAgBK,MAChBA,GACErD,GACGgF,EAAQ4V,GAAazc,EAAM0C,SAASpC,EAAQuZ,eAAend,IAC5DggB,EAAM1c,EAAM+B,OAChBzB,EAAQuZ,eAAend,GAAMyH,IAAIoD,IAE7BoV,EAAY3c,EAAM+B,OAAO8E,GACzBvD,EAAQtD,EAAM+B,OAAOrF,GACrBkgB,EAAY5c,EAAM+B,QAAO,GAE/BuB,EAAMtB,QAAUtF,EAChBigB,EAAU3a,QAAU6E,EACpBvG,EAAQwD,OAAOiB,MAAMb,IAAIxH,GAEzBwI,GACG5E,EAAkC2E,SACjCvI,EACAwI,GAGJtD,EAAa,CACXU,KAAM,EACJiC,SACA7H,KAAMmgB,MAKN,GAAIA,IAAmBvZ,EAAMtB,UAAY6a,EAAgB,CACvD,MAAM1N,EAAc5Q,EAAIgG,EAAQjB,EAAMtB,SAClC9F,MAAMC,QAAQgT,KAChBsN,EAAUtN,GACVuN,EAAI1a,QAAUmN,EAAYhL,IAAIoD,GAEjC,GAEHnF,QAAS9B,EAAQkD,UAAUuB,QAG7B,MAAM+X,EAAe9c,EAAM+c,aAMvBC,IAEAJ,EAAU5a,SAAU,EACpB1B,EAAQ+Y,kBAAkB3c,EAAMsgB,EAAwB,GAE1D,CAAC1c,EAAS5D,IA6QZ,OApGAsD,EAAMiC,WAAU,KAQd,GAPA3B,EAAQmG,OAAOC,QAAS,EAExBkC,EAAUlM,EAAM4D,EAAQwD,SACtBxD,EAAQkD,UAAUC,MAAMnB,KAAK,IACxBhC,EAAQqC,aAIbia,EAAU5a,WACRqG,EAAmB/H,EAAQgG,SAASgC,MAAMC,YAC1CjI,EAAQqC,WAAWsP,aAErB,GAAI3R,EAAQgG,SAASqM,SACnBrS,EAAQsS,eAAe,CAAClW,IAAOqd,MAAMpb,IACnC,MAAM6G,EAAQjH,EAAII,EAAO0E,OAAQ3G,GAC3BugB,EAAgB1e,EAAI+B,EAAQqC,WAAWU,OAAQ3G,IAGnDugB,GACMzX,GAASyX,EAActhB,MACxB6J,IACEyX,EAActhB,OAAS6J,EAAM7J,MAC5BshB,EAAc9W,UAAYX,EAAMW,SACpCX,GAASA,EAAM7J,QAEnB6J,EACIpG,EAAIkB,EAAQqC,WAAWU,OAAQ3G,EAAM8I,GACrCoJ,GAAMtO,EAAQqC,WAAWU,OAAQ3G,GACrC4D,EAAQkD,UAAUC,MAAMnB,KAAK,CAC3Be,OAAQ/C,EAAQqC,WAAWU,SAE9B,QAEE,CACL,MAAMoC,EAAelH,EAAI+B,EAAQwF,QAASpJ,IAExC+I,IACAA,EAAMM,IAEJsC,EAAmB/H,EAAQgG,SAASsL,gBAAgBrJ,YACpDF,EAAmB/H,EAAQgG,SAASgC,MAAMC,YAG5CsC,GACEpF,EACAnF,EAAQwD,OAAO3B,SACf7B,EAAQmE,YACRnE,EAAQgG,SAASmM,eAAiB5S,EAClCS,EAAQgG,SAAS0E,2BACjB,GACA+O,MACCvU,IACEzE,EAAcyE,IACflF,EAAQkD,UAAUC,MAAMnB,KAAK,CAC3Be,OAAQgG,EACN/I,EAAQqC,WAAWU,OACnBmC,EACA9I,MAKX,CAGH4D,EAAQkD,UAAUe,OAAOjC,KAAK,CAC5B5F,OACA6H,OAAQ,IAAKjE,EAAQmE,eAGvBnE,EAAQwD,OAAOkC,OACbgD,EAAsB1I,EAAQwF,SAAS,CAACF,EAAK5H,KAC3C,GACEsC,EAAQwD,OAAOkC,OACfhI,EAAI2D,WAAWrB,EAAQwD,OAAOkC,QAC9BJ,EAAII,MAGJ,OADAJ,EAAII,QACG,CAEF,IAGX1F,EAAQwD,OAAOkC,MAAQ,GAEvB1F,EAAQoD,eAERkZ,EAAU5a,SAAU,CAAK,GACxB,CAAC6E,EAAQnK,EAAM4D,IAElBN,EAAMiC,WAAU,MACb1D,EAAI+B,EAAQmE,YAAa/H,IAAS4D,EAAQ+Y,kBAAkB3c,GAEtD,MACJ4D,EAAQgG,SAASzB,kBAAoBA,IACpCvE,EAAQqG,WAAWjK,EAAgC,IAEtD,CAACA,EAAM4D,EAASkc,EAAS3X,IAErB,CACLqY,KAAMld,EAAM+c,aA1LD,CAACrO,EAAgBC,KAC5B,MAAMqO,EAA0B1c,EAAQuZ,eAAend,GACvD+R,GAAYuO,EAAyBtO,EAAQC,GAC7CF,GAAYiO,EAAI1a,QAAS0M,EAAQC,GACjCmO,EAAaE,GACbP,EAAUO,GACV1c,EAAQ+Y,kBACN3c,EACAsgB,EACAvO,GACA,CACEiL,KAAMhL,EACNiL,KAAMhL,IAER,EACD,GA2K6B,CAACmO,EAAcpgB,EAAM4D,IACnD6c,KAAMnd,EAAM+c,aAzKD,CAAClP,EAAcC,KAC1B,MAAMkP,EAA0B1c,EAAQuZ,eAAend,GACvDkR,GAAYoP,EAAyBnP,EAAMC,GAC3CF,GAAY8O,EAAI1a,QAAS6L,EAAMC,GAC/BgP,EAAaE,GACbP,EAAUO,GACV1c,EAAQ+Y,kBACN3c,EACAsgB,EACApP,GACA,CACE8L,KAAM7L,EACN8L,KAAM7L,IAER,EACD,GA0J6B,CAACgP,EAAcpgB,EAAM4D,IACnD8c,QAASpd,EAAM+c,aArPD,CACdlhB,EAGAoM,KAEA,MAAMoV,EAAehc,EAAsBlE,EAAYtB,IACjDmhB,EAA0BhP,GAC9B1N,EAAQuZ,eAAend,GACvB2gB,GAEF/c,EAAQwD,OAAOkC,MAAQgC,EAAkBtL,EAAM,EAAGuL,GAClDyU,EAAI1a,QAAUgM,GAAU0O,EAAI1a,QAASqb,EAAalZ,IAAIoD,IACtDuV,EAAaE,GACbP,EAAUO,GACV1c,EAAQ+Y,kBAAkB3c,EAAMsgB,EAAyBhP,GAAW,CAClE0L,KAAMhM,GAAe7R,IACrB,GAoOkC,CAACihB,EAAcpgB,EAAM4D,IACzDub,OAAQ7b,EAAM+c,aA9QD,CACblhB,EAGAoM,KAEA,MAAMqV,EAAcjc,EAAsBlE,EAAYtB,IAChDmhB,EAA0BvP,GAC9BnN,EAAQuZ,eAAend,GACvB4gB,GAEFhd,EAAQwD,OAAOkC,MAAQgC,EACrBtL,EACAsgB,EAAwBzd,OAAS,EACjC0I,GAEFyU,EAAI1a,QAAUyL,GAASiP,EAAI1a,QAASsb,EAAYnZ,IAAIoD,IACpDuV,EAAaE,GACbP,EAAUO,GACV1c,EAAQ+Y,kBAAkB3c,EAAMsgB,EAAyBvP,GAAU,CACjEiM,KAAMhM,GAAe7R,IACrB,GAyPgC,CAACihB,EAAcpgB,EAAM4D,IACvDid,OAAQvd,EAAM+c,aAnOA1d,IACd,MAAM2d,EAEA/O,GAAc3N,EAAQuZ,eAAend,GAAO2C,GAClDqd,EAAI1a,QAAUiM,GAAcyO,EAAI1a,QAAS3C,GACzCyd,EAAaE,GACbP,EAAUO,IACT9gB,MAAMC,QAAQoC,EAAI+B,EAAQwF,QAASpJ,KAClC0C,EAAIkB,EAAQwF,QAASpJ,OAAM4B,GAC7BgC,EAAQ+Y,kBAAkB3c,EAAMsgB,EAAyB/O,GAAe,CACtEyL,KAAMra,GACN,GAwNgC,CAACyd,EAAcpgB,EAAM4D,IACvDqN,OAAQ3N,EAAM+c,aAtND,CACb1d,EACAxD,EAGAoM,KAEA,MAAMuV,EAAcnc,EAAsBlE,EAAYtB,IAChDmhB,EAA0BS,GAC9Bnd,EAAQuZ,eAAend,GACvB2C,EACAme,GAEFld,EAAQwD,OAAOkC,MAAQgC,EAAkBtL,EAAM2C,EAAO4I,GACtDyU,EAAI1a,QAAUyb,GAASf,EAAI1a,QAAS3C,EAAOme,EAAYrZ,IAAIoD,IAC3DuV,EAAaE,GACbP,EAAUO,GACV1c,EAAQ+Y,kBAAkB3c,EAAMsgB,EAAyBS,GAAU,CACjE/D,KAAMra,EACNsa,KAAMjM,GAAe7R,IACrB,GAkMgC,CAACihB,EAAcpgB,EAAM4D,IACvDod,OAAQ1d,EAAM+c,aA5JD,CACb1d,EACAxD,KAEA,MAAM2I,EAAcrH,EAAYtB,GAC1BmhB,EAA0B9N,GAC9B5O,EAAQuZ,eAENnd,GACF2C,EACAmF,GAEFkY,EAAI1a,QAAU,IAAIgb,GAAyB7Y,KAAI,CAACwZ,EAAMxP,IACnDwP,GAAQxP,IAAM9O,EAAuBqd,EAAI1a,QAAQmM,GAA3B5G,MAEzBuV,EAAaE,GACbP,EAAU,IAAIO,IACd1c,EAAQ+Y,kBACN3c,EACAsgB,EACA9N,GACA,CACEwK,KAAMra,EACNsa,KAAMnV,IAER,GACA,EACD,GAiIiC,CAACsY,EAAcpgB,EAAM4D,IACvDnB,QAASa,EAAM+c,aA9HflhB,IAIA,MAAMmhB,EAA0B3b,EAAsBlE,EAAYtB,IAClE6gB,EAAI1a,QAAUgb,EAAwB7Y,IAAIoD,GAC1CuV,EAAa,IAAIE,IACjBP,EAAU,IAAIO,IACd1c,EAAQ+Y,kBACN3c,EACA,IAAIsgB,IACA5f,GAAeA,GACnB,CAAA,GACA,GACA,EACD,GA+GmC,CAAC0f,EAAcpgB,EAAM4D,IACzDuG,OAAQ7G,EAAM2D,SACZ,IACEkD,EAAO1C,KAAI,CAACsB,EAAOpG,KAAW,IACzBoG,EACH+W,CAACA,GAAUE,EAAI1a,QAAQ3C,IAAUkI,SAErC,CAACV,EAAQ2V,IAGf,kBClYgB,SAKd3a,EAA8C,IAE9C,MAAM+b,EAAe5d,EAAM+B,YAEzBzD,GACIuf,EAAU7d,EAAM+B,YAA4BzD,IAC3C+B,EAAWc,GAAmBnB,EAAM0C,SAAkC,CAC3EI,SAAS,EACTK,cAAc,EACdJ,UAAWyG,GAAW3H,EAAMpB,eAC5BwR,aAAa,EACbC,cAAc,EACdC,oBAAoB,EACpB/O,SAAS,EACT4O,YAAa,EACbhP,YAAa,CAAE,EACfC,cAAe,CAAE,EACjBC,iBAAkB,CAAE,EACpBG,OAAQxB,EAAMwB,QAAU,CAAE,EAC1BlB,SAAUN,EAAMM,WAAY,EAC5B1B,cAAe+I,GAAW3H,EAAMpB,oBAC5BnC,EACAuD,EAAMpB,gBAGPmd,EAAa5b,UAChB4b,EAAa5b,QAAU,IAClB8P,GAAkBjQ,GACrBxB,cAIJ,MAAMC,EAAUsd,EAAa5b,QAAQ1B,QA4ErC,OA3EAA,EAAQgG,SAAWzE,EAEnBD,EAAa,CACXQ,QAAS9B,EAAQkD,UAAUC,MAC3BnB,KACEzG,IAGEoF,EACEpF,EACAyE,EAAQQ,gBACRR,EAAQiD,kBACR,IAGFpC,EAAgB,IAAKb,EAAQqC,YAC9B,IAIL3C,EAAMiC,WACJ,IAAM3B,EAAQ2Z,aAAapY,EAAMM,WACjC,CAAC7B,EAASuB,EAAMM,WAGlBnC,EAAMiC,WAAU,KACd,GAAI3B,EAAQQ,gBAAgBgC,QAAS,CACnC,MAAMA,EAAUxC,EAAQqT,YACpB7Q,IAAYzC,EAAUyC,SACxBxC,EAAQkD,UAAUC,MAAMnB,KAAK,CAC3BQ,WAGL,IACA,CAACxC,EAASD,EAAUyC,UAEvB9C,EAAMiC,WAAU,KACVJ,EAAM0C,SAAWoL,GAAU9N,EAAM0C,OAAQsZ,EAAQ7b,UACnD1B,EAAQ+X,OAAOxW,EAAM0C,OAAQjE,EAAQgG,SAAS0T,cAC9C6D,EAAQ7b,QAAUH,EAAM0C,OACxBpD,GAAiBsC,IAAK,IAAWA,OAEjCnD,EAAQwZ,qBACT,GACA,CAACjY,EAAM0C,OAAQjE,IAElBN,EAAMiC,WAAU,KACVJ,EAAMwB,QACR/C,EAAQ4Z,WAAWrY,EAAMwB,OAC1B,GACA,CAACxB,EAAMwB,OAAQ/C,IAElBN,EAAMiC,WAAU,KACT3B,EAAQmG,OAAOD,QAClBlG,EAAQoD,eACRpD,EAAQmG,OAAOD,OAAQ,GAGrBlG,EAAQmG,OAAOxC,QACjB3D,EAAQmG,OAAOxC,OAAQ,EACvB3D,EAAQkD,UAAUC,MAAMnB,KAAK,IAAKhC,EAAQqC,cAG5CrC,EAAQqE,kBAAkB,IAG5B3E,EAAMiC,WAAU,KACdJ,EAAMgD,kBACJvE,EAAQkD,UAAUe,OAAOjC,KAAK,CAC5BiC,OAAQjE,EAAQoE,aAChB,GACH,CAAC7C,EAAMgD,iBAAkBvE,IAE5Bsd,EAAa5b,QAAQ3B,UAAYD,EAAkBC,EAAWC,GAEvDsd,EAAa5b,OACtB"}