!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react")):"function"==typeof define&&define.amd?define(["exports","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).ReactHookForm={},e.React)}(this,(function(e,t){"use strict";var r=e=>"checkbox"===e.type,s=e=>e instanceof Date,a=e=>null==e;const i=e=>"object"==typeof e;var n=e=>!a(e)&&!Array.isArray(e)&&i(e)&&!s(e),o=e=>n(e)&&e.target?r(e.target)?e.target.checked:e.target.value:e,u=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),l="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function d(e){let t;const r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(l&&(e instanceof Blob||s)||!r&&!n(e))return e;if(t=r?[]:{},r||(e=>{const t=e.constructor&&e.constructor.prototype;return n(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=d(e[r]));else t=e}return t}var c=e=>Array.isArray(e)?e.filter(Boolean):[],f=e=>void 0===e,m=(e,t,r)=>{if(!t||!n(e))return r;const s=c(t.split(/[,[\].]+?/)).reduce(((e,t)=>a(e)?e:e[t]),e);return f(s)||s===e?f(e[t])?r:e[t]:s},y=e=>"boolean"==typeof e,g=e=>/^\w*$/.test(e),_=e=>c(e.replace(/["|']|\]/g,"").split(/\.|\[/)),p=(e,t,r)=>{let s=-1;const a=g(t)?[t]:_(t),i=a.length,o=i-1;for(;++s<i;){const t=a[s];let i=r;if(s!==o){const r=e[t];i=n(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=i,e=e[t]}return e};const v="blur",b="focusout",h="change",A="onBlur",V="onChange",F="onSubmit",x="onTouched",S="all",w="max",k="min",D="maxLength",E="minLength",C="pattern",j="required",O="validate",T=t.createContext(null),N=()=>t.useContext(T);var M=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==S&&(t._proxyFormState[a]=!s||S),r&&(r[a]=!0),e[a]}});return a},U=e=>n(e)&&!Object.keys(e).length,B=(e,t,r,s)=>{r(e);const{name:a,...i}=e;return U(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find((e=>t[e]===(!s||S)))},L=e=>Array.isArray(e)?e:[e],R=(e,t,r)=>!e||!t||e===t||L(e).some((e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))));function P(e){const r=t.useRef(e);r.current=e,t.useEffect((()=>{const t=!e.disabled&&r.current.subject&&r.current.subject.subscribe({next:r.current.next});return()=>{t&&t.unsubscribe()}}),[e.disabled])}function q(e){const r=N(),{control:s=r.control,disabled:a,name:i,exact:n}=e||{},[o,u]=t.useState(s._formState),l=t.useRef(!0),d=t.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),c=t.useRef(i);return c.current=i,P({disabled:a,next:e=>l.current&&R(c.current,e.name,n)&&B(e,d.current,s._updateFormState)&&u({...s._formState,...e}),subject:s._subjects.state}),t.useEffect((()=>(l.current=!0,d.current.isValid&&s._updateValid(!0),()=>{l.current=!1})),[s]),t.useMemo((()=>M(o,s,d.current,!1)),[o,s])}var W=e=>"string"==typeof e,$=(e,t,r,s,a)=>W(e)?(s&&t.watch.add(e),m(r,e,a)):Array.isArray(e)?e.map((e=>(s&&t.watch.add(e),m(r,e)))):(s&&(t.watchAll=!0),r);function I(e){const r=N(),{control:s=r.control,name:a,defaultValue:i,disabled:n,exact:o}=e||{},u=t.useRef(a);u.current=a,P({disabled:n,subject:s._subjects.values,next:e=>{R(u.current,e.name,o)&&c(d($(u.current,s._names,e.values||s._formValues,!1,i)))}});const[l,c]=t.useState(s._getWatch(a,i));return t.useEffect((()=>s._removeUnmounted())),l}function H(e){const r=N(),{name:s,disabled:a,control:i=r.control,shouldUnregister:n}=e,l=u(i._names.array,s),c=I({control:i,name:s,defaultValue:m(i._formValues,s,m(i._defaultValues,s,e.defaultValue)),exact:!0}),g=q({control:i,name:s,exact:!0}),_=t.useRef(i.register(s,{...e.rules,value:c,...y(e.disabled)?{disabled:e.disabled}:{}})),b=t.useMemo((()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!m(g.errors,s)},isDirty:{enumerable:!0,get:()=>!!m(g.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!m(g.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!m(g.validatingFields,s)},error:{enumerable:!0,get:()=>m(g.errors,s)}})),[g,s]),A=t.useMemo((()=>({name:s,value:c,...y(a)||g.disabled?{disabled:g.disabled||a}:{},onChange:e=>_.current.onChange({target:{value:o(e),name:s},type:h}),onBlur:()=>_.current.onBlur({target:{value:m(i._formValues,s),name:s},type:v}),ref:e=>{const t=m(i._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus(),select:()=>e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})}})),[s,i._formValues,a,g.disabled,c,i._fields]);return t.useEffect((()=>{const e=i._options.shouldUnregister||n,t=(e,t)=>{const r=m(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=d(m(i._options.defaultValues,s));p(i._defaultValues,s,e),f(m(i._formValues,s))&&p(i._formValues,s,e)}return!l&&i.register(s),()=>{(l?e&&!i._state.action:e)?i.unregister(s):t(s,!1)}}),[s,i,l,n]),t.useEffect((()=>{i._updateDisabledField({disabled:a,fields:i._fields,name:s})}),[a,s,i]),t.useMemo((()=>({field:A,formState:g,fieldState:b})),[A,g,b])}const J=e=>{const t={};for(const r of Object.keys(e))if(i(e[r])&&null!==e[r]){const s=J(e[r]);for(const e of Object.keys(s))t[`${r}.${e}`]=s[e]}else t[r]=e[r];return t},z="post";var G=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},K=()=>{const e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)}))},Q=(e,t,r={})=>r.shouldFocus||f(r.shouldFocus)?r.focusName||`${e}.${f(r.focusIndex)?t:r.focusIndex}.`:"",X=e=>({isOnSubmit:!e||e===F,isOnBlur:e===A,isOnChange:e===V,isOnAll:e===S,isOnTouch:e===x}),Y=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const Z=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=m(e,a);if(r){const{_f:e,...i}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(Z(i,t))break}else if(n(i)&&Z(i,t))break}}};var ee=(e,t,r)=>{const s=L(m(e,r));return p(s,"root",t[r]),p(e,r,s),e},te=e=>"file"===e.type,re=e=>"function"==typeof e,se=e=>{if(!l)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},ae=e=>W(e),ie=e=>"radio"===e.type,ne=e=>e instanceof RegExp;const oe={value:!1,isValid:!1},ue={value:!0,isValid:!0};var le=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!f(e[0].attributes.value)?f(e[0].value)||""===e[0].value?ue:{value:e[0].value,isValid:!0}:ue:oe}return oe};const de={isValid:!1,value:null};var ce=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),de):de;function fe(e,t,r="validate"){if(ae(e)||Array.isArray(e)&&e.every(ae)||y(e)&&!e)return{type:r,message:ae(e)?e:"",ref:t}}var me=e=>n(e)&&!ne(e)?e:{value:e,message:""},ye=async(e,t,s,i,o,u)=>{const{ref:l,refs:d,required:c,maxLength:g,minLength:_,min:p,max:v,pattern:b,validate:h,name:A,valueAsNumber:V,mount:F}=e._f,x=m(s,A);if(!F||t.has(A))return{};const S=d?d[0]:l,T=e=>{o&&S.reportValidity&&(S.setCustomValidity(y(e)?"":e||""),S.reportValidity())},N={},M=ie(l),B=r(l),L=M||B,R=(V||te(l))&&f(l.value)&&f(x)||se(l)&&""===l.value||""===x||Array.isArray(x)&&!x.length,P=G.bind(null,A,i,N),q=(e,t,r,s=D,a=E)=>{const i=e?t:r;N[A]={type:e?s:a,message:i,ref:l,...P(e?s:a,i)}};if(u?!Array.isArray(x)||!x.length:c&&(!L&&(R||a(x))||y(x)&&!x||B&&!le(d).isValid||M&&!ce(d).isValid)){const{value:e,message:t}=ae(c)?{value:!!c,message:c}:me(c);if(e&&(N[A]={type:j,message:t,ref:S,...P(j,t)},!i))return T(t),N}if(!(R||a(p)&&a(v))){let e,t;const r=me(v),s=me(p);if(a(x)||isNaN(x)){const a=l.valueAsDate||new Date(x),i=e=>new Date((new Date).toDateString()+" "+e),n="time"==l.type,o="week"==l.type;W(r.value)&&x&&(e=n?i(x)>i(r.value):o?x>r.value:a>new Date(r.value)),W(s.value)&&x&&(t=n?i(x)<i(s.value):o?x<s.value:a<new Date(s.value))}else{const i=l.valueAsNumber||(x?+x:x);a(r.value)||(e=i>r.value),a(s.value)||(t=i<s.value)}if((e||t)&&(q(!!e,r.message,s.message,w,k),!i))return T(N[A].message),N}if((g||_)&&!R&&(W(x)||u&&Array.isArray(x))){const e=me(g),t=me(_),r=!a(e.value)&&x.length>+e.value,s=!a(t.value)&&x.length<+t.value;if((r||s)&&(q(r,e.message,t.message),!i))return T(N[A].message),N}if(b&&!R&&W(x)){const{value:e,message:t}=me(b);if(ne(e)&&!x.match(e)&&(N[A]={type:C,message:t,ref:l,...P(C,t)},!i))return T(t),N}if(h)if(re(h)){const e=fe(await h(x,s),S);if(e&&(N[A]={...e,...P(O,e.message)},!i))return T(e.message),N}else if(n(h)){let e={};for(const t in h){if(!U(e)&&!i)break;const r=fe(await h[t](x,s),S,t);r&&(e={...r,...P(t,r.message)},T(r.message),i&&(N[A]=e))}if(!U(e)&&(N[A]={ref:S,...e},!i))return N}return T(!0),N},ge=(e,t)=>[...e,...L(t)],_e=e=>Array.isArray(e)?e.map((()=>{})):void 0;function pe(e,t,r){return[...e.slice(0,t),...L(r),...e.slice(t)]}var ve=(e,t,r)=>Array.isArray(e)?(f(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],be=(e,t)=>[...L(t),...L(e)];var he=(e,t)=>f(t)?[]:function(e,t){let r=0;const s=[...e];for(const e of t)s.splice(e-r,1),r++;return c(s).length?s:[]}(e,L(t).sort(((e,t)=>e-t))),Ae=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]};function Ve(e,t){const r=Array.isArray(t)?t:g(t)?[t]:_(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=f(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,i=r[a];return s&&delete s[i],0!==a&&(n(s)&&U(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!f(e[t]))return!1;return!0}(s))&&Ve(e,r.slice(0,-1)),e}var Fe=(e,t,r)=>(e[t]=r,e);var xe=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},Se=e=>a(e)||!i(e);function we(e,t){if(Se(e)||Se(t))return e===t;if(s(e)&&s(t))return e.getTime()===t.getTime();const r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;for(const i of r){const r=e[i];if(!a.includes(i))return!1;if("ref"!==i){const e=t[i];if(s(r)&&s(e)||n(r)&&n(e)||Array.isArray(r)&&Array.isArray(e)?!we(r,e):r!==e)return!1}}return!0}var ke=e=>"select-multiple"===e.type,De=e=>se(e)&&e.isConnected,Ee=e=>{for(const t in e)if(re(e[t]))return!0;return!1};function Ce(e,t={}){const r=Array.isArray(e);if(n(e)||r)for(const r in e)Array.isArray(e[r])||n(e[r])&&!Ee(e[r])?(t[r]=Array.isArray(e[r])?[]:{},Ce(e[r],t[r])):a(e[r])||(t[r]=!0);return t}function je(e,t,r){const s=Array.isArray(e);if(n(e)||s)for(const s in e)Array.isArray(e[s])||n(e[s])&&!Ee(e[s])?f(t)||Se(r[s])?r[s]=Array.isArray(e[s])?Ce(e[s],[]):{...Ce(e[s])}:je(e[s],a(t)?{}:t[s],r[s]):r[s]=!we(e[s],t[s]);return r}var Oe=(e,t)=>je(e,t,Ce(t)),Te=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>f(e)?e:t?""===e?NaN:e?+e:e:r&&W(e)?new Date(e):s?s(e):e;function Ne(e){const t=e.ref;return te(t)?t.files:ie(t)?ce(e.refs).value:ke(t)?[...t.selectedOptions].map((({value:e})=>e)):r(t)?le(e.refs).value:Te(f(t.value)?e.ref.value:t.value,e)}var Me=e=>f(e)?e:ne(e)?e.source:n(e)?ne(e.value)?e.value.source:e.value:e;const Ue="AsyncFunction";function Be(e,t,r){const s=m(e,r);if(s||g(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=m(t,s),n=m(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};a.pop()}return{name:r}}const Le={mode:F,reValidateMode:V,shouldFocusError:!0};function Re(e={}){let t,i={...Le,...e},g={submitCount:0,isDirty:!1,isLoading:re(i.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:i.errors||{},disabled:i.disabled||!1},_={},h=(n(i.defaultValues)||n(i.values))&&d(i.defaultValues||i.values)||{},A=i.shouldUnregister?{}:d(h),V={action:!1,mount:!1,watch:!1},F={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},x=0;const w={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},k={values:xe(),array:xe(),state:xe()},D=X(i.mode),E=X(i.reValidateMode),C=i.criteriaMode===S,j=async e=>{if(!i.disabled&&(w.isValid||e)){const e=i.resolver?U((await B()).errors):await R(_,!0);e!==g.isValid&&k.state.next({isValid:e})}},O=(e,t)=>{i.disabled||!w.isValidating&&!w.validatingFields||((e||Array.from(F.mount)).forEach((e=>{e&&(t?p(g.validatingFields,e,t):Ve(g.validatingFields,e))})),k.state.next({validatingFields:g.validatingFields,isValidating:!U(g.validatingFields)}))},T=(e,t,r,s)=>{const a=m(_,e);if(a){const i=m(A,e,f(r)?m(h,e):r);f(i)||s&&s.defaultChecked||t?p(A,e,t?i:Ne(a._f)):I(e,i),V.mount&&j()}},N=(e,t,r,s,a)=>{let n=!1,o=!1;const u={name:e};if(!i.disabled){const i=!!(m(_,e)&&m(_,e)._f&&m(_,e)._f.disabled);if(!r||s){w.isDirty&&(o=g.isDirty,g.isDirty=u.isDirty=P(),n=o!==u.isDirty);const r=i||we(m(h,e),t);o=!(i||!m(g.dirtyFields,e)),r||i?Ve(g.dirtyFields,e):p(g.dirtyFields,e,!0),u.dirtyFields=g.dirtyFields,n=n||w.dirtyFields&&o!==!r}if(r){const t=m(g.touchedFields,e);t||(p(g.touchedFields,e,r),u.touchedFields=g.touchedFields,n=n||w.touchedFields&&t!==r)}n&&a&&k.state.next(u)}return n?u:{}},M=(e,r,s,a)=>{const n=m(g.errors,e),o=w.isValid&&y(r)&&g.isValid!==r;var u;if(i.delayError&&s?(u=()=>((e,t)=>{p(g.errors,e,t),k.state.next({errors:g.errors})})(e,s),t=e=>{clearTimeout(x),x=setTimeout(u,e)},t(i.delayError)):(clearTimeout(x),t=null,s?p(g.errors,e,s):Ve(g.errors,e)),(s?!we(n,s):n)||!U(a)||o){const t={...a,...o&&y(r)?{isValid:r}:{},errors:g.errors,name:e};g={...g,...t},k.state.next(t)}},B=async e=>{O(e,!0);const t=await i.resolver(A,i.context,((e,t,r,s)=>{const a={};for(const r of e){const e=m(t,r);e&&p(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||F.mount,_,i.criteriaMode,i.shouldUseNativeValidation));return O(e),t},R=async(e,t,r={valid:!0})=>{for(const a in e){const o=e[a];if(o){const{_f:e,...u}=o;if(e){const u=F.array.has(e.name),l=o._f&&(!!(s=o._f)&&!!s.validate&&!!(re(s.validate)&&s.validate.constructor.name===Ue||n(s.validate)&&Object.values(s.validate).find((e=>e.constructor.name===Ue))));l&&w.validatingFields&&O([a],!0);const d=await ye(o,F.disabled,A,C,i.shouldUseNativeValidation&&!t,u);if(l&&w.validatingFields&&O([a]),d[e.name]&&(r.valid=!1,t))break;!t&&(m(d,e.name)?u?ee(g.errors,d,e.name):p(g.errors,e.name,d[e.name]):Ve(g.errors,e.name))}!U(u)&&await R(u,t,r)}}var s;return r.valid},P=(e,t)=>!i.disabled&&(e&&t&&p(A,e,t),!we(Q(),h)),q=(e,t,r)=>$(e,F,{...V.mount?A:f(t)?h:W(e)?{[e]:t}:t},r,t),I=(e,t,s={})=>{const i=m(_,e);let n=t;if(i){const s=i._f;s&&(!s.disabled&&p(A,e,Te(t,s)),n=se(s.ref)&&a(t)?"":t,ke(s.ref)?[...s.ref.options].forEach((e=>e.selected=n.includes(e.value))):s.refs?r(s.ref)?s.refs.length>1?s.refs.forEach((e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(n)?!!n.find((t=>t===e.value)):n===e.value))):s.refs[0]&&(s.refs[0].checked=!!n):s.refs.forEach((e=>e.checked=e.value===n)):te(s.ref)?s.ref.value="":(s.ref.value=n,s.ref.type||k.values.next({name:e,values:{...A}})))}(s.shouldDirty||s.shouldTouch)&&N(e,n,s.shouldTouch,s.shouldDirty,!0),s.shouldValidate&&K(e)},H=(e,t,r)=>{for(const a in t){const i=t[a],o=`${e}.${a}`,u=m(_,o);(F.array.has(e)||n(i)||u&&!u._f)&&!s(i)?H(o,i,r):I(o,i,r)}},J=(e,t,r={})=>{const s=m(_,e),i=F.array.has(e),n=d(t);p(A,e,n),i?(k.array.next({name:e,values:{...A}}),(w.isDirty||w.dirtyFields)&&r.shouldDirty&&k.state.next({name:e,dirtyFields:Oe(h,A),isDirty:P(e,n)})):!s||s._f||a(n)?I(e,n,r):H(e,n,r),Y(e,F)&&k.state.next({...g}),k.values.next({name:V.mount?e:void 0,values:{...A}})},z=async e=>{V.mount=!0;const r=e.target;let a=r.name,n=!0;const u=m(_,a),l=e=>{n=Number.isNaN(e)||s(e)&&isNaN(e.getTime())||we(e,m(A,a,e))};if(u){let s,c;const f=r.type?Ne(u._f):o(e),y=e.type===v||e.type===b,h=!((d=u._f).mount&&(d.required||d.min||d.max||d.maxLength||d.minLength||d.pattern||d.validate)||i.resolver||m(g.errors,a)||u._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(y,m(g.touchedFields,a),g.isSubmitted,E,D),V=Y(a,F,y);p(A,a,f),y?(u._f.onBlur&&u._f.onBlur(e),t&&t(0)):u._f.onChange&&u._f.onChange(e);const x=N(a,f,y,!1),S=!U(x)||V;if(!y&&k.values.next({name:a,type:e.type,values:{...A}}),h)return w.isValid&&("onBlur"===i.mode&&y?j():y||j()),S&&k.state.next({name:a,...V?{}:x});if(!y&&V&&k.state.next({...g}),i.resolver){const{errors:e}=await B([a]);if(l(f),n){const t=Be(g.errors,_,a),r=Be(e,_,t.name||a);s=r.error,a=r.name,c=U(e)}}else O([a],!0),s=(await ye(u,F.disabled,A,C,i.shouldUseNativeValidation))[a],O([a]),l(f),n&&(s?c=!1:w.isValid&&(c=await R(_,!0)));n&&(u._f.deps&&K(u._f.deps),M(a,c,s,x))}var d},G=(e,t)=>{if(m(g.errors,t)&&e.focus)return e.focus(),1},K=async(e,t={})=>{let r,s;const a=L(e);if(i.resolver){const t=await(async e=>{const{errors:t}=await B(e);if(e)for(const r of e){const e=m(t,r);e?p(g.errors,r,e):Ve(g.errors,r)}else g.errors=t;return t})(f(e)?e:a);r=U(t),s=e?!a.some((e=>m(t,e))):r}else e?(s=(await Promise.all(a.map((async e=>{const t=m(_,e);return await R(t&&t._f?{[e]:t}:t)})))).every(Boolean),(s||g.isValid)&&j()):s=r=await R(_);return k.state.next({...!W(e)||w.isValid&&r!==g.isValid?{}:{name:e},...i.resolver||!e?{isValid:r}:{},errors:g.errors}),t.shouldFocus&&!s&&Z(_,G,e?a:F.mount),s},Q=e=>{const t={...V.mount?A:h};return f(e)?t:W(e)?m(t,e):e.map((e=>m(t,e)))},ae=(e,t)=>({invalid:!!m((t||g).errors,e),isDirty:!!m((t||g).dirtyFields,e),error:m((t||g).errors,e),isValidating:!!m(g.validatingFields,e),isTouched:!!m((t||g).touchedFields,e)}),ne=(e,t,r)=>{const s=(m(_,e,{_f:{}})._f||{}).ref,a=m(g.errors,e)||{},{ref:i,message:n,type:o,...u}=a;p(g.errors,e,{...u,...t,ref:s}),k.state.next({name:e,errors:g.errors,isValid:!1}),r&&r.shouldFocus&&s&&s.focus&&s.focus()},oe=(e,t={})=>{for(const r of e?L(e):F.mount)F.mount.delete(r),F.array.delete(r),t.keepValue||(Ve(_,r),Ve(A,r)),!t.keepError&&Ve(g.errors,r),!t.keepDirty&&Ve(g.dirtyFields,r),!t.keepTouched&&Ve(g.touchedFields,r),!t.keepIsValidating&&Ve(g.validatingFields,r),!i.shouldUnregister&&!t.keepDefaultValue&&Ve(h,r);k.values.next({values:{...A}}),k.state.next({...g,...t.keepDirty?{isDirty:P()}:{}}),!t.keepIsValid&&j()},ue=({disabled:e,name:t,field:r,fields:s})=>{(y(e)&&V.mount||e||F.disabled.has(t))&&(e?F.disabled.add(t):F.disabled.delete(t),N(t,Ne(r?r._f:m(s,t)._f),!1,!1,!0))},le=(e,t={})=>{let s=m(_,e);const a=y(t.disabled)||y(i.disabled);return p(_,e,{...s||{},_f:{...s&&s._f?s._f:{ref:{name:e}},name:e,mount:!0,...t}}),F.mount.add(e),s?ue({field:s,disabled:y(t.disabled)?t.disabled:i.disabled,name:e}):T(e,!0,t.value),{...a?{disabled:t.disabled||i.disabled}:{},...i.progressive?{required:!!t.required,min:Me(t.min),max:Me(t.max),minLength:Me(t.minLength),maxLength:Me(t.maxLength),pattern:Me(t.pattern)}:{},name:e,onChange:z,onBlur:z,ref:a=>{if(a){le(e,t),s=m(_,e);const i=f(a.value)&&a.querySelectorAll&&a.querySelectorAll("input,select,textarea")[0]||a,n=(e=>ie(e)||r(e))(i),o=s._f.refs||[];if(n?o.find((e=>e===i)):i===s._f.ref)return;p(_,e,{_f:{...s._f,...n?{refs:[...o.filter(De),i,...Array.isArray(m(h,e))?[{}]:[]],ref:{type:i.type,name:e}}:{ref:i}}}),T(e,!1,void 0,i)}else s=m(_,e,{}),s._f&&(s._f.mount=!1),(i.shouldUnregister||t.shouldUnregister)&&(!u(F.array,e)||!V.action)&&F.unMount.add(e)}}},de=()=>i.shouldFocusError&&Z(_,G,F.mount),ce=(e,t)=>async r=>{let s;r&&(r.preventDefault&&r.preventDefault(),r.persist&&r.persist());let a=d(A);if(F.disabled.size)for(const e of F.disabled)p(a,e,void 0);if(k.state.next({isSubmitting:!0}),i.resolver){const{errors:e,values:t}=await B();g.errors=e,a=t}else await R(_);if(Ve(g.errors,"root"),U(g.errors)){k.state.next({errors:{}});try{await e(a,r)}catch(e){s=e}}else t&&await t({...g.errors},r),de(),setTimeout(de);if(k.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:U(g.errors)&&!s,submitCount:g.submitCount+1,errors:g.errors}),s)throw s},fe=(e,t={})=>{const r=e?d(e):h,s=d(r),a=U(e),n=a?h:s;if(t.keepDefaultValues||(h=r),!t.keepValues){if(t.keepDirtyValues){const e=new Set([...F.mount,...Object.keys(Oe(h,A))]);for(const t of Array.from(e))m(g.dirtyFields,t)?p(n,t,m(A,t)):J(t,m(n,t))}else{if(l&&f(e))for(const e of F.mount){const t=m(_,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(se(e)){const t=e.closest("form");if(t){t.reset();break}}}}_={}}A=i.shouldUnregister?t.keepDefaultValues?d(h):{}:d(n),k.array.next({values:{...n}}),k.values.next({values:{...n}})}F={mount:t.keepDirtyValues?F.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},V.mount=!w.isValid||!!t.keepIsValid||!!t.keepDirtyValues,V.watch=!!i.shouldUnregister,k.state.next({submitCount:t.keepSubmitCount?g.submitCount:0,isDirty:!a&&(t.keepDirty?g.isDirty:!(!t.keepDefaultValues||we(e,h))),isSubmitted:!!t.keepIsSubmitted&&g.isSubmitted,dirtyFields:a?{}:t.keepDirtyValues?t.keepDefaultValues&&A?Oe(h,A):g.dirtyFields:t.keepDefaultValues&&e?Oe(h,e):t.keepDirty?g.dirtyFields:{},touchedFields:t.keepTouched?g.touchedFields:{},errors:t.keepErrors?g.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&g.isSubmitSuccessful,isSubmitting:!1})},me=(e,t)=>fe(re(e)?e(A):e,t);return{control:{register:le,unregister:oe,getFieldState:ae,handleSubmit:ce,setError:ne,_executeSchema:B,_getWatch:q,_getDirty:P,_updateValid:j,_removeUnmounted:()=>{for(const e of F.unMount){const t=m(_,e);t&&(t._f.refs?t._f.refs.every((e=>!De(e))):!De(t._f.ref))&&oe(e)}F.unMount=new Set},_updateFieldArray:(e,t=[],r,s,a=!0,n=!0)=>{if(s&&r&&!i.disabled){if(V.action=!0,n&&Array.isArray(m(_,e))){const t=r(m(_,e),s.argA,s.argB);a&&p(_,e,t)}if(n&&Array.isArray(m(g.errors,e))){const t=r(m(g.errors,e),s.argA,s.argB);a&&p(g.errors,e,t),((e,t)=>{!c(m(e,t)).length&&Ve(e,t)})(g.errors,e)}if(w.touchedFields&&n&&Array.isArray(m(g.touchedFields,e))){const t=r(m(g.touchedFields,e),s.argA,s.argB);a&&p(g.touchedFields,e,t)}w.dirtyFields&&(g.dirtyFields=Oe(h,A)),k.state.next({name:e,isDirty:P(e,t),dirtyFields:g.dirtyFields,errors:g.errors,isValid:g.isValid})}else p(A,e,t)},_updateDisabledField:ue,_getFieldArray:e=>c(m(V.mount?A:h,e,i.shouldUnregister?m(h,e,[]):[])),_reset:fe,_resetDefaultValues:()=>re(i.defaultValues)&&i.defaultValues().then((e=>{me(e,i.resetOptions),k.state.next({isLoading:!1})})),_updateFormState:e=>{g={...g,...e}},_disableForm:e=>{y(e)&&(k.state.next({disabled:e}),Z(_,((t,r)=>{const s=m(_,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach((t=>{t.disabled=s._f.disabled||e})))}),0,!1))},_subjects:k,_proxyFormState:w,_setErrors:e=>{g.errors=e,k.state.next({errors:g.errors,isValid:!1})},get _fields(){return _},get _formValues(){return A},get _state(){return V},set _state(e){V=e},get _defaultValues(){return h},get _names(){return F},set _names(e){F=e},get _formState(){return g},set _formState(e){g=e},get _options(){return i},set _options(e){i={...i,...e}}},trigger:K,register:le,handleSubmit:ce,watch:(e,t)=>re(e)?k.values.subscribe({next:r=>e(q(void 0,t),r)}):q(e,t,!0),setValue:J,getValues:Q,reset:me,resetField:(e,t={})=>{m(_,e)&&(f(t.defaultValue)?J(e,d(m(h,e))):(J(e,t.defaultValue),p(h,e,d(t.defaultValue))),t.keepTouched||Ve(g.touchedFields,e),t.keepDirty||(Ve(g.dirtyFields,e),g.isDirty=t.defaultValue?P(e,d(m(h,e))):P()),t.keepError||(Ve(g.errors,e),w.isValid&&j()),k.state.next({...g}))},clearErrors:e=>{e&&L(e).forEach((e=>Ve(g.errors,e))),k.state.next({errors:e?g.errors:{}})},unregister:oe,setError:ne,setFocus:(e,t={})=>{const r=m(_,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&re(e.select)&&e.select())}},getFieldState:ae}}e.Controller=e=>e.render(H(e)),e.Form=function(e){const r=N(),[s,a]=t.useState(!1),{control:i=r.control,onSubmit:n,children:o,action:u,method:l=z,headers:d,encType:c,onError:f,render:m,onSuccess:y,validateStatus:g,..._}=e,p=async t=>{let r=!1,s="";await i.handleSubmit((async e=>{const a=new FormData;let o="";try{o=JSON.stringify(e)}catch(e){}const m=J(i._formValues);for(const e in m)a.append(e,m[e]);if(n&&await n({data:e,event:t,method:l,formData:a,formDataJson:o}),u)try{const e=[d&&d["Content-Type"],c].some((e=>e&&e.includes("json"))),t=await fetch(String(u),{method:l,headers:{...d,...c?{"Content-Type":c}:{}},body:e?o:a});t&&(g?!g(t.status):t.status<200||t.status>=300)?(r=!0,f&&f({response:t}),s=String(t.status)):y&&y({response:t})}catch(e){r=!0,f&&f({error:e})}}))(t),r&&e.control&&(e.control._subjects.state.next({isSubmitSuccessful:!1}),e.control.setError("root.server",{type:s}))};return t.useEffect((()=>{a(!0)}),[]),m?t.createElement(t.Fragment,null,m({submit:p})):t.createElement("form",{noValidate:s,action:u,method:l,encType:c,onSubmit:p,..._},o)},e.FormProvider=e=>{const{children:r,...s}=e;return t.createElement(T.Provider,{value:s},r)},e.appendErrors=G,e.get=m,e.set=p,e.useController=H,e.useFieldArray=function(e){const r=N(),{control:s=r.control,name:a,keyName:i="id",shouldUnregister:n,rules:o}=e,[u,l]=t.useState(s._getFieldArray(a)),c=t.useRef(s._getFieldArray(a).map(K)),f=t.useRef(u),y=t.useRef(a),g=t.useRef(!1);y.current=a,f.current=u,s._names.array.add(a),o&&s.register(a,o),P({next:({values:e,name:t})=>{if(t===y.current||!t){const t=m(e,y.current);Array.isArray(t)&&(l(t),c.current=t.map(K))}},subject:s._subjects.array});const _=t.useCallback((e=>{g.current=!0,s._updateFieldArray(a,e)}),[s,a]);return t.useEffect((()=>{if(s._state.action=!1,Y(a,s._names)&&s._subjects.state.next({...s._formState}),g.current&&(!X(s._options.mode).isOnSubmit||s._formState.isSubmitted))if(s._options.resolver)s._executeSchema([a]).then((e=>{const t=m(e.errors,a),r=m(s._formState.errors,a);(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type)&&(t?p(s._formState.errors,a,t):Ve(s._formState.errors,a),s._subjects.state.next({errors:s._formState.errors}))}));else{const e=m(s._fields,a);!e||!e._f||X(s._options.reValidateMode).isOnSubmit&&X(s._options.mode).isOnSubmit||ye(e,s._names.disabled,s._formValues,s._options.criteriaMode===S,s._options.shouldUseNativeValidation,!0).then((e=>!U(e)&&s._subjects.state.next({errors:ee(s._formState.errors,e,a)})))}s._subjects.values.next({name:a,values:{...s._formValues}}),s._names.focus&&Z(s._fields,((e,t)=>{if(s._names.focus&&t.startsWith(s._names.focus)&&e.focus)return e.focus(),1})),s._names.focus="",s._updateValid(),g.current=!1}),[u,a,s]),t.useEffect((()=>(!m(s._formValues,a)&&s._updateFieldArray(a),()=>{(s._options.shouldUnregister||n)&&s.unregister(a)})),[a,s,i,n]),{swap:t.useCallback(((e,t)=>{const r=s._getFieldArray(a);Ae(r,e,t),Ae(c.current,e,t),_(r),l(r),s._updateFieldArray(a,r,Ae,{argA:e,argB:t},!1)}),[_,a,s]),move:t.useCallback(((e,t)=>{const r=s._getFieldArray(a);ve(r,e,t),ve(c.current,e,t),_(r),l(r),s._updateFieldArray(a,r,ve,{argA:e,argB:t},!1)}),[_,a,s]),prepend:t.useCallback(((e,t)=>{const r=L(d(e)),i=be(s._getFieldArray(a),r);s._names.focus=Q(a,0,t),c.current=be(c.current,r.map(K)),_(i),l(i),s._updateFieldArray(a,i,be,{argA:_e(e)})}),[_,a,s]),append:t.useCallback(((e,t)=>{const r=L(d(e)),i=ge(s._getFieldArray(a),r);s._names.focus=Q(a,i.length-1,t),c.current=ge(c.current,r.map(K)),_(i),l(i),s._updateFieldArray(a,i,ge,{argA:_e(e)})}),[_,a,s]),remove:t.useCallback((e=>{const t=he(s._getFieldArray(a),e);c.current=he(c.current,e),_(t),l(t),!Array.isArray(m(s._fields,a))&&p(s._fields,a,void 0),s._updateFieldArray(a,t,he,{argA:e})}),[_,a,s]),insert:t.useCallback(((e,t,r)=>{const i=L(d(t)),n=pe(s._getFieldArray(a),e,i);s._names.focus=Q(a,e,r),c.current=pe(c.current,e,i.map(K)),_(n),l(n),s._updateFieldArray(a,n,pe,{argA:e,argB:_e(t)})}),[_,a,s]),update:t.useCallback(((e,t)=>{const r=d(t),i=Fe(s._getFieldArray(a),e,r);c.current=[...i].map(((t,r)=>t&&r!==e?c.current[r]:K())),_(i),l([...i]),s._updateFieldArray(a,i,Fe,{argA:e,argB:r},!0,!1)}),[_,a,s]),replace:t.useCallback((e=>{const t=L(d(e));c.current=t.map(K),_([...t]),l([...t]),s._updateFieldArray(a,[...t],(e=>e),{},!0,!1)}),[_,a,s]),fields:t.useMemo((()=>u.map(((e,t)=>({...e,[i]:c.current[t]||K()})))),[u,i])}},e.useForm=function(e={}){const r=t.useRef(void 0),s=t.useRef(void 0),[a,i]=t.useState({isDirty:!1,isValidating:!1,isLoading:re(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:re(e.defaultValues)?void 0:e.defaultValues});r.current||(r.current={...Re(e),formState:a});const n=r.current.control;return n._options=e,P({subject:n._subjects.state,next:e=>{B(e,n._proxyFormState,n._updateFormState,!0)&&i({...n._formState})}}),t.useEffect((()=>n._disableForm(e.disabled)),[n,e.disabled]),t.useEffect((()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}}),[n,a.isDirty]),t.useEffect((()=>{e.values&&!we(e.values,s.current)?(n._reset(e.values,n._options.resetOptions),s.current=e.values,i((e=>({...e})))):n._resetDefaultValues()}),[e.values,n]),t.useEffect((()=>{e.errors&&n._setErrors(e.errors)}),[e.errors,n]),t.useEffect((()=>{n._state.mount||(n._updateValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()})),t.useEffect((()=>{e.shouldUnregister&&n._subjects.values.next({values:n._getWatch()})}),[e.shouldUnregister,n]),r.current.formState=M(a,n),r.current},e.useFormContext=N,e.useFormState=q,e.useWatch=I}));
//# sourceMappingURL=index.umd.js.map
