{"syntax": 3, "package": null, "imports": [], "enums": [], "messages": [{"name": "OptionFields", "enums": [], "extends": [], "messages": [], "options": {}, "fields": [{"name": "type", "type": "string", "tag": 2, "map": null, "oneof": null, "required": false, "repeated": false, "options": {"mylist": "\"some,values,[here]\""}}], "extensions": null}, {"name": "MoreOptionFields", "enums": [], "extends": [], "messages": [], "options": {}, "fields": [{"name": "values", "type": "string", "tag": 3, "map": null, "oneof": null, "required": false, "repeated": false, "options": {"mylist2": "'[more, values], [here]'"}}], "extensions": null}], "options": {}, "extends": []}