"use strict";var e=require("react"),t=e=>"checkbox"===e.type,r=e=>e instanceof Date,s=e=>null==e;const a=e=>"object"==typeof e;var i=e=>!s(e)&&!Array.isArray(e)&&a(e)&&!r(e),n=e=>i(e)&&e.target?t(e.target)?e.target.checked:e.target.value:e,o=(e,t)=>e.has((e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e)(t)),u="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function l(e){let t;const r=Array.isArray(e),s="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else if(e instanceof Set)t=new Set(e);else{if(u&&(e instanceof Blob||s)||!r&&!i(e))return e;if(t=r?[]:{},r||(e=>{const t=e.constructor&&e.constructor.prototype;return i(t)&&t.hasOwnProperty("isPrototypeOf")})(e))for(const r in e)e.hasOwnProperty(r)&&(t[r]=l(e[r]));else t=e}return t}var d=e=>Array.isArray(e)?e.filter(Boolean):[],c=e=>void 0===e,f=(e,t,r)=>{if(!t||!i(e))return r;const a=d(t.split(/[,[\].]+?/)).reduce(((e,t)=>s(e)?e:e[t]),e);return c(a)||a===e?c(e[t])?r:e[t]:a},m=e=>"boolean"==typeof e,y=e=>/^\w*$/.test(e),p=e=>d(e.replace(/["|']|\]/g,"").split(/\.|\[/)),_=(e,t,r)=>{let s=-1;const a=y(t)?[t]:p(t),n=a.length,o=n-1;for(;++s<n;){const t=a[s];let n=r;if(s!==o){const r=e[t];n=i(r)||Array.isArray(r)?r:isNaN(+a[s+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=n,e=e[t]}return e};const g="blur",v="focusout",b="change",h="onBlur",x="onChange",A="onSubmit",V="onTouched",F="all",S="max",w="min",k="maxLength",D="minLength",E="pattern",C="required",j="validate",O=e.createContext(null),T=()=>e.useContext(O);var N=(e,t,r,s=!0)=>{const a={defaultValues:t._defaultValues};for(const i in e)Object.defineProperty(a,i,{get:()=>{const a=i;return t._proxyFormState[a]!==F&&(t._proxyFormState[a]=!s||F),r&&(r[a]=!0),e[a]}});return a},M=e=>i(e)&&!Object.keys(e).length,U=(e,t,r,s)=>{r(e);const{name:a,...i}=e;return M(i)||Object.keys(i).length>=Object.keys(t).length||Object.keys(i).find((e=>t[e]===(!s||F)))},B=e=>Array.isArray(e)?e:[e],L=(e,t,r)=>!e||!t||e===t||B(e).some((e=>e&&(r?e===t:e.startsWith(t)||t.startsWith(e))));function R(t){const r=e.useRef(t);r.current=t,e.useEffect((()=>{const e=!t.disabled&&r.current.subject&&r.current.subject.subscribe({next:r.current.next});return()=>{e&&e.unsubscribe()}}),[t.disabled])}function P(t){const r=T(),{control:s=r.control,disabled:a,name:i,exact:n}=t||{},[o,u]=e.useState(s._formState),l=e.useRef(!0),d=e.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),c=e.useRef(i);return c.current=i,R({disabled:a,next:e=>l.current&&L(c.current,e.name,n)&&U(e,d.current,s._updateFormState)&&u({...s._formState,...e}),subject:s._subjects.state}),e.useEffect((()=>(l.current=!0,d.current.isValid&&s._updateValid(!0),()=>{l.current=!1})),[s]),e.useMemo((()=>N(o,s,d.current,!1)),[o,s])}var q=e=>"string"==typeof e,W=(e,t,r,s,a)=>q(e)?(s&&t.watch.add(e),f(r,e,a)):Array.isArray(e)?e.map((e=>(s&&t.watch.add(e),f(r,e)))):(s&&(t.watchAll=!0),r);function $(t){const r=T(),{control:s=r.control,name:a,defaultValue:i,disabled:n,exact:o}=t||{},u=e.useRef(a);u.current=a,R({disabled:n,subject:s._subjects.values,next:e=>{L(u.current,e.name,o)&&c(l(W(u.current,s._names,e.values||s._formValues,!1,i)))}});const[d,c]=e.useState(s._getWatch(a,i));return e.useEffect((()=>s._removeUnmounted())),d}function I(t){const r=T(),{name:s,disabled:a,control:i=r.control,shouldUnregister:u}=t,d=o(i._names.array,s),y=$({control:i,name:s,defaultValue:f(i._formValues,s,f(i._defaultValues,s,t.defaultValue)),exact:!0}),p=P({control:i,name:s,exact:!0}),v=e.useRef(i.register(s,{...t.rules,value:y,...m(t.disabled)?{disabled:t.disabled}:{}})),h=e.useMemo((()=>Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!f(p.errors,s)},isDirty:{enumerable:!0,get:()=>!!f(p.dirtyFields,s)},isTouched:{enumerable:!0,get:()=>!!f(p.touchedFields,s)},isValidating:{enumerable:!0,get:()=>!!f(p.validatingFields,s)},error:{enumerable:!0,get:()=>f(p.errors,s)}})),[p,s]),x=e.useMemo((()=>({name:s,value:y,...m(a)||p.disabled?{disabled:p.disabled||a}:{},onChange:e=>v.current.onChange({target:{value:n(e),name:s},type:b}),onBlur:()=>v.current.onBlur({target:{value:f(i._formValues,s),name:s},type:g}),ref:e=>{const t=f(i._fields,s);t&&e&&(t._f.ref={focus:()=>e.focus(),select:()=>e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()})}})),[s,i._formValues,a,p.disabled,y,i._fields]);return e.useEffect((()=>{const e=i._options.shouldUnregister||u,t=(e,t)=>{const r=f(i._fields,e);r&&r._f&&(r._f.mount=t)};if(t(s,!0),e){const e=l(f(i._options.defaultValues,s));_(i._defaultValues,s,e),c(f(i._formValues,s))&&_(i._formValues,s,e)}return!d&&i.register(s),()=>{(d?e&&!i._state.action:e)?i.unregister(s):t(s,!1)}}),[s,i,d,u]),e.useEffect((()=>{i._updateDisabledField({disabled:a,fields:i._fields,name:s})}),[a,s,i]),e.useMemo((()=>({field:x,formState:p,fieldState:h})),[x,p,h])}const H=e=>{const t={};for(const r of Object.keys(e))if(a(e[r])&&null!==e[r]){const s=H(e[r]);for(const e of Object.keys(s))t[`${r}.${e}`]=s[e]}else t[r]=e[r];return t},J="post";var z=(e,t,r,s,a)=>t?{...r[e],types:{...r[e]&&r[e].types?r[e].types:{},[s]:a||!0}}:{},G=()=>{const e="undefined"==typeof performance?Date.now():1e3*performance.now();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const r=(16*Math.random()+e)%16|0;return("x"==t?r:3&r|8).toString(16)}))},K=(e,t,r={})=>r.shouldFocus||c(r.shouldFocus)?r.focusName||`${e}.${c(r.focusIndex)?t:r.focusIndex}.`:"",Q=e=>({isOnSubmit:!e||e===A,isOnBlur:e===h,isOnChange:e===x,isOnAll:e===F,isOnTouch:e===V}),X=(e,t,r)=>!r&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const Y=(e,t,r,s)=>{for(const a of r||Object.keys(e)){const r=f(e,a);if(r){const{_f:e,...n}=r;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!s)return!0;if(e.ref&&t(e.ref,e.name)&&!s)return!0;if(Y(n,t))break}else if(i(n)&&Y(n,t))break}}};var Z=(e,t,r)=>{const s=B(f(e,r));return _(s,"root",t[r]),_(e,r,s),e},ee=e=>"file"===e.type,te=e=>"function"==typeof e,re=e=>{if(!u)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},se=e=>q(e),ae=e=>"radio"===e.type,ie=e=>e instanceof RegExp;const ne={value:!1,isValid:!1},oe={value:!0,isValid:!0};var ue=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!c(e[0].attributes.value)?c(e[0].value)||""===e[0].value?oe:{value:e[0].value,isValid:!0}:oe:ne}return ne};const le={isValid:!1,value:null};var de=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e),le):le;function ce(e,t,r="validate"){if(se(e)||Array.isArray(e)&&e.every(se)||m(e)&&!e)return{type:r,message:se(e)?e:"",ref:t}}var fe=e=>i(e)&&!ie(e)?e:{value:e,message:""},me=async(e,r,a,n,o,u)=>{const{ref:l,refs:d,required:y,maxLength:p,minLength:_,min:g,max:v,pattern:b,validate:h,name:x,valueAsNumber:A,mount:V}=e._f,F=f(a,x);if(!V||r.has(x))return{};const O=d?d[0]:l,T=e=>{o&&O.reportValidity&&(O.setCustomValidity(m(e)?"":e||""),O.reportValidity())},N={},U=ae(l),B=t(l),L=U||B,R=(A||ee(l))&&c(l.value)&&c(F)||re(l)&&""===l.value||""===F||Array.isArray(F)&&!F.length,P=z.bind(null,x,n,N),W=(e,t,r,s=k,a=D)=>{const i=e?t:r;N[x]={type:e?s:a,message:i,ref:l,...P(e?s:a,i)}};if(u?!Array.isArray(F)||!F.length:y&&(!L&&(R||s(F))||m(F)&&!F||B&&!ue(d).isValid||U&&!de(d).isValid)){const{value:e,message:t}=se(y)?{value:!!y,message:y}:fe(y);if(e&&(N[x]={type:C,message:t,ref:O,...P(C,t)},!n))return T(t),N}if(!(R||s(g)&&s(v))){let e,t;const r=fe(v),a=fe(g);if(s(F)||isNaN(F)){const s=l.valueAsDate||new Date(F),i=e=>new Date((new Date).toDateString()+" "+e),n="time"==l.type,o="week"==l.type;q(r.value)&&F&&(e=n?i(F)>i(r.value):o?F>r.value:s>new Date(r.value)),q(a.value)&&F&&(t=n?i(F)<i(a.value):o?F<a.value:s<new Date(a.value))}else{const i=l.valueAsNumber||(F?+F:F);s(r.value)||(e=i>r.value),s(a.value)||(t=i<a.value)}if((e||t)&&(W(!!e,r.message,a.message,S,w),!n))return T(N[x].message),N}if((p||_)&&!R&&(q(F)||u&&Array.isArray(F))){const e=fe(p),t=fe(_),r=!s(e.value)&&F.length>+e.value,a=!s(t.value)&&F.length<+t.value;if((r||a)&&(W(r,e.message,t.message),!n))return T(N[x].message),N}if(b&&!R&&q(F)){const{value:e,message:t}=fe(b);if(ie(e)&&!F.match(e)&&(N[x]={type:E,message:t,ref:l,...P(E,t)},!n))return T(t),N}if(h)if(te(h)){const e=ce(await h(F,a),O);if(e&&(N[x]={...e,...P(j,e.message)},!n))return T(e.message),N}else if(i(h)){let e={};for(const t in h){if(!M(e)&&!n)break;const r=ce(await h[t](F,a),O,t);r&&(e={...r,...P(t,r.message)},T(r.message),n&&(N[x]=e))}if(!M(e)&&(N[x]={ref:O,...e},!n))return N}return T(!0),N},ye=(e,t)=>[...e,...B(t)],pe=e=>Array.isArray(e)?e.map((()=>{})):void 0;function _e(e,t,r){return[...e.slice(0,t),...B(r),...e.slice(t)]}var ge=(e,t,r)=>Array.isArray(e)?(c(e[r])&&(e[r]=void 0),e.splice(r,0,e.splice(t,1)[0]),e):[],ve=(e,t)=>[...B(t),...B(e)];var be=(e,t)=>c(t)?[]:function(e,t){let r=0;const s=[...e];for(const e of t)s.splice(e-r,1),r++;return d(s).length?s:[]}(e,B(t).sort(((e,t)=>e-t))),he=(e,t,r)=>{[e[t],e[r]]=[e[r],e[t]]};function xe(e,t){const r=Array.isArray(t)?t:y(t)?[t]:p(t),s=1===r.length?e:function(e,t){const r=t.slice(0,-1).length;let s=0;for(;s<r;)e=c(e)?s++:e[t[s++]];return e}(e,r),a=r.length-1,n=r[a];return s&&delete s[n],0!==a&&(i(s)&&M(s)||Array.isArray(s)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!c(e[t]))return!1;return!0}(s))&&xe(e,r.slice(0,-1)),e}var Ae=(e,t,r)=>(e[t]=r,e);var Ve=()=>{let e=[];return{get observers(){return e},next:t=>{for(const r of e)r.next&&r.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter((e=>e!==t))}}),unsubscribe:()=>{e=[]}}},Fe=e=>s(e)||!a(e);function Se(e,t){if(Fe(e)||Fe(t))return e===t;if(r(e)&&r(t))return e.getTime()===t.getTime();const s=Object.keys(e),a=Object.keys(t);if(s.length!==a.length)return!1;for(const n of s){const s=e[n];if(!a.includes(n))return!1;if("ref"!==n){const e=t[n];if(r(s)&&r(e)||i(s)&&i(e)||Array.isArray(s)&&Array.isArray(e)?!Se(s,e):s!==e)return!1}}return!0}var we=e=>"select-multiple"===e.type,ke=e=>re(e)&&e.isConnected,De=e=>{for(const t in e)if(te(e[t]))return!0;return!1};function Ee(e,t={}){const r=Array.isArray(e);if(i(e)||r)for(const r in e)Array.isArray(e[r])||i(e[r])&&!De(e[r])?(t[r]=Array.isArray(e[r])?[]:{},Ee(e[r],t[r])):s(e[r])||(t[r]=!0);return t}function Ce(e,t,r){const a=Array.isArray(e);if(i(e)||a)for(const a in e)Array.isArray(e[a])||i(e[a])&&!De(e[a])?c(t)||Fe(r[a])?r[a]=Array.isArray(e[a])?Ee(e[a],[]):{...Ee(e[a])}:Ce(e[a],s(t)?{}:t[a],r[a]):r[a]=!Se(e[a],t[a]);return r}var je=(e,t)=>Ce(e,t,Ee(t)),Oe=(e,{valueAsNumber:t,valueAsDate:r,setValueAs:s})=>c(e)?e:t?""===e?NaN:e?+e:e:r&&q(e)?new Date(e):s?s(e):e;function Te(e){const r=e.ref;return ee(r)?r.files:ae(r)?de(e.refs).value:we(r)?[...r.selectedOptions].map((({value:e})=>e)):t(r)?ue(e.refs).value:Oe(c(r.value)?e.ref.value:r.value,e)}var Ne=e=>c(e)?e:ie(e)?e.source:i(e)?ie(e.value)?e.value.source:e.value:e;const Me="AsyncFunction";function Ue(e,t,r){const s=f(e,r);if(s||y(r))return{error:s,name:r};const a=r.split(".");for(;a.length;){const s=a.join("."),i=f(t,s),n=f(e,s);if(i&&!Array.isArray(i)&&r!==s)return{name:r};if(n&&n.type)return{name:s,error:n};a.pop()}return{name:r}}const Be={mode:A,reValidateMode:x,shouldFocusError:!0};function Le(e={}){let a,y={...Be,...e},p={submitCount:0,isDirty:!1,isLoading:te(y.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:y.errors||{},disabled:y.disabled||!1},b={},h=(i(y.defaultValues)||i(y.values))&&l(y.defaultValues||y.values)||{},x=y.shouldUnregister?{}:l(h),A={action:!1,mount:!1,watch:!1},V={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},S=0;const w={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},k={values:Ve(),array:Ve(),state:Ve()},D=Q(y.mode),E=Q(y.reValidateMode),C=y.criteriaMode===F,j=async e=>{if(!y.disabled&&(w.isValid||e)){const e=y.resolver?M((await L()).errors):await R(b,!0);e!==p.isValid&&k.state.next({isValid:e})}},O=(e,t)=>{y.disabled||!w.isValidating&&!w.validatingFields||((e||Array.from(V.mount)).forEach((e=>{e&&(t?_(p.validatingFields,e,t):xe(p.validatingFields,e))})),k.state.next({validatingFields:p.validatingFields,isValidating:!M(p.validatingFields)}))},T=(e,t,r,s)=>{const a=f(b,e);if(a){const i=f(x,e,c(r)?f(h,e):r);c(i)||s&&s.defaultChecked||t?_(x,e,t?i:Te(a._f)):I(e,i),A.mount&&j()}},N=(e,t,r,s,a)=>{let i=!1,n=!1;const o={name:e};if(!y.disabled){const u=!!(f(b,e)&&f(b,e)._f&&f(b,e)._f.disabled);if(!r||s){w.isDirty&&(n=p.isDirty,p.isDirty=o.isDirty=P(),i=n!==o.isDirty);const r=u||Se(f(h,e),t);n=!(u||!f(p.dirtyFields,e)),r||u?xe(p.dirtyFields,e):_(p.dirtyFields,e,!0),o.dirtyFields=p.dirtyFields,i=i||w.dirtyFields&&n!==!r}if(r){const t=f(p.touchedFields,e);t||(_(p.touchedFields,e,r),o.touchedFields=p.touchedFields,i=i||w.touchedFields&&t!==r)}i&&a&&k.state.next(o)}return i?o:{}},U=(e,t,r,s)=>{const i=f(p.errors,e),n=w.isValid&&m(t)&&p.isValid!==t;var o;if(y.delayError&&r?(o=()=>((e,t)=>{_(p.errors,e,t),k.state.next({errors:p.errors})})(e,r),a=e=>{clearTimeout(S),S=setTimeout(o,e)},a(y.delayError)):(clearTimeout(S),a=null,r?_(p.errors,e,r):xe(p.errors,e)),(r?!Se(i,r):i)||!M(s)||n){const r={...s,...n&&m(t)?{isValid:t}:{},errors:p.errors,name:e};p={...p,...r},k.state.next(r)}},L=async e=>{O(e,!0);const t=await y.resolver(x,y.context,((e,t,r,s)=>{const a={};for(const r of e){const e=f(t,r);e&&_(a,r,e._f)}return{criteriaMode:r,names:[...e],fields:a,shouldUseNativeValidation:s}})(e||V.mount,b,y.criteriaMode,y.shouldUseNativeValidation));return O(e),t},R=async(e,t,r={valid:!0})=>{for(const a in e){const n=e[a];if(n){const{_f:e,...o}=n;if(e){const o=V.array.has(e.name),u=n._f&&(!!(s=n._f)&&!!s.validate&&!!(te(s.validate)&&s.validate.constructor.name===Me||i(s.validate)&&Object.values(s.validate).find((e=>e.constructor.name===Me))));u&&w.validatingFields&&O([a],!0);const l=await me(n,V.disabled,x,C,y.shouldUseNativeValidation&&!t,o);if(u&&w.validatingFields&&O([a]),l[e.name]&&(r.valid=!1,t))break;!t&&(f(l,e.name)?o?Z(p.errors,l,e.name):_(p.errors,e.name,l[e.name]):xe(p.errors,e.name))}!M(o)&&await R(o,t,r)}}var s;return r.valid},P=(e,t)=>!y.disabled&&(e&&t&&_(x,e,t),!Se(se(),h)),$=(e,t,r)=>W(e,V,{...A.mount?x:c(t)?h:q(e)?{[e]:t}:t},r,t),I=(e,r,a={})=>{const i=f(b,e);let n=r;if(i){const a=i._f;a&&(!a.disabled&&_(x,e,Oe(r,a)),n=re(a.ref)&&s(r)?"":r,we(a.ref)?[...a.ref.options].forEach((e=>e.selected=n.includes(e.value))):a.refs?t(a.ref)?a.refs.length>1?a.refs.forEach((e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(n)?!!n.find((t=>t===e.value)):n===e.value))):a.refs[0]&&(a.refs[0].checked=!!n):a.refs.forEach((e=>e.checked=e.value===n)):ee(a.ref)?a.ref.value="":(a.ref.value=n,a.ref.type||k.values.next({name:e,values:{...x}})))}(a.shouldDirty||a.shouldTouch)&&N(e,n,a.shouldTouch,a.shouldDirty,!0),a.shouldValidate&&K(e)},H=(e,t,s)=>{for(const a in t){const n=t[a],o=`${e}.${a}`,u=f(b,o);(V.array.has(e)||i(n)||u&&!u._f)&&!r(n)?H(o,n,s):I(o,n,s)}},J=(e,t,r={})=>{const a=f(b,e),i=V.array.has(e),n=l(t);_(x,e,n),i?(k.array.next({name:e,values:{...x}}),(w.isDirty||w.dirtyFields)&&r.shouldDirty&&k.state.next({name:e,dirtyFields:je(h,x),isDirty:P(e,n)})):!a||a._f||s(n)?I(e,n,r):H(e,n,r),X(e,V)&&k.state.next({...p}),k.values.next({name:A.mount?e:void 0,values:{...x}})},z=async e=>{A.mount=!0;const t=e.target;let s=t.name,i=!0;const o=f(b,s),u=e=>{i=Number.isNaN(e)||r(e)&&isNaN(e.getTime())||Se(e,f(x,s,e))};if(o){let r,d;const c=t.type?Te(o._f):n(e),m=e.type===g||e.type===v,h=!((l=o._f).mount&&(l.required||l.min||l.max||l.maxLength||l.minLength||l.pattern||l.validate)||y.resolver||f(p.errors,s)||o._f.deps)||((e,t,r,s,a)=>!a.isOnAll&&(!r&&a.isOnTouch?!(t||e):(r?s.isOnBlur:a.isOnBlur)?!e:!(r?s.isOnChange:a.isOnChange)||e))(m,f(p.touchedFields,s),p.isSubmitted,E,D),A=X(s,V,m);_(x,s,c),m?(o._f.onBlur&&o._f.onBlur(e),a&&a(0)):o._f.onChange&&o._f.onChange(e);const F=N(s,c,m,!1),S=!M(F)||A;if(!m&&k.values.next({name:s,type:e.type,values:{...x}}),h)return w.isValid&&("onBlur"===y.mode&&m?j():m||j()),S&&k.state.next({name:s,...A?{}:F});if(!m&&A&&k.state.next({...p}),y.resolver){const{errors:e}=await L([s]);if(u(c),i){const t=Ue(p.errors,b,s),a=Ue(e,b,t.name||s);r=a.error,s=a.name,d=M(e)}}else O([s],!0),r=(await me(o,V.disabled,x,C,y.shouldUseNativeValidation))[s],O([s]),u(c),i&&(r?d=!1:w.isValid&&(d=await R(b,!0)));i&&(o._f.deps&&K(o._f.deps),U(s,d,r,F))}var l},G=(e,t)=>{if(f(p.errors,t)&&e.focus)return e.focus(),1},K=async(e,t={})=>{let r,s;const a=B(e);if(y.resolver){const t=await(async e=>{const{errors:t}=await L(e);if(e)for(const r of e){const e=f(t,r);e?_(p.errors,r,e):xe(p.errors,r)}else p.errors=t;return t})(c(e)?e:a);r=M(t),s=e?!a.some((e=>f(t,e))):r}else e?(s=(await Promise.all(a.map((async e=>{const t=f(b,e);return await R(t&&t._f?{[e]:t}:t)})))).every(Boolean),(s||p.isValid)&&j()):s=r=await R(b);return k.state.next({...!q(e)||w.isValid&&r!==p.isValid?{}:{name:e},...y.resolver||!e?{isValid:r}:{},errors:p.errors}),t.shouldFocus&&!s&&Y(b,G,e?a:V.mount),s},se=e=>{const t={...A.mount?x:h};return c(e)?t:q(e)?f(t,e):e.map((e=>f(t,e)))},ie=(e,t)=>({invalid:!!f((t||p).errors,e),isDirty:!!f((t||p).dirtyFields,e),error:f((t||p).errors,e),isValidating:!!f(p.validatingFields,e),isTouched:!!f((t||p).touchedFields,e)}),ne=(e,t,r)=>{const s=(f(b,e,{_f:{}})._f||{}).ref,a=f(p.errors,e)||{},{ref:i,message:n,type:o,...u}=a;_(p.errors,e,{...u,...t,ref:s}),k.state.next({name:e,errors:p.errors,isValid:!1}),r&&r.shouldFocus&&s&&s.focus&&s.focus()},oe=(e,t={})=>{for(const r of e?B(e):V.mount)V.mount.delete(r),V.array.delete(r),t.keepValue||(xe(b,r),xe(x,r)),!t.keepError&&xe(p.errors,r),!t.keepDirty&&xe(p.dirtyFields,r),!t.keepTouched&&xe(p.touchedFields,r),!t.keepIsValidating&&xe(p.validatingFields,r),!y.shouldUnregister&&!t.keepDefaultValue&&xe(h,r);k.values.next({values:{...x}}),k.state.next({...p,...t.keepDirty?{isDirty:P()}:{}}),!t.keepIsValid&&j()},ue=({disabled:e,name:t,field:r,fields:s})=>{(m(e)&&A.mount||e||V.disabled.has(t))&&(e?V.disabled.add(t):V.disabled.delete(t),N(t,Te(r?r._f:f(s,t)._f),!1,!1,!0))},le=(e,r={})=>{let s=f(b,e);const a=m(r.disabled)||m(y.disabled);return _(b,e,{...s||{},_f:{...s&&s._f?s._f:{ref:{name:e}},name:e,mount:!0,...r}}),V.mount.add(e),s?ue({field:s,disabled:m(r.disabled)?r.disabled:y.disabled,name:e}):T(e,!0,r.value),{...a?{disabled:r.disabled||y.disabled}:{},...y.progressive?{required:!!r.required,min:Ne(r.min),max:Ne(r.max),minLength:Ne(r.minLength),maxLength:Ne(r.maxLength),pattern:Ne(r.pattern)}:{},name:e,onChange:z,onBlur:z,ref:a=>{if(a){le(e,r),s=f(b,e);const i=c(a.value)&&a.querySelectorAll&&a.querySelectorAll("input,select,textarea")[0]||a,n=(e=>ae(e)||t(e))(i),o=s._f.refs||[];if(n?o.find((e=>e===i)):i===s._f.ref)return;_(b,e,{_f:{...s._f,...n?{refs:[...o.filter(ke),i,...Array.isArray(f(h,e))?[{}]:[]],ref:{type:i.type,name:e}}:{ref:i}}}),T(e,!1,void 0,i)}else s=f(b,e,{}),s._f&&(s._f.mount=!1),(y.shouldUnregister||r.shouldUnregister)&&(!o(V.array,e)||!A.action)&&V.unMount.add(e)}}},de=()=>y.shouldFocusError&&Y(b,G,V.mount),ce=(e,t)=>async r=>{let s;r&&(r.preventDefault&&r.preventDefault(),r.persist&&r.persist());let a=l(x);if(V.disabled.size)for(const e of V.disabled)_(a,e,void 0);if(k.state.next({isSubmitting:!0}),y.resolver){const{errors:e,values:t}=await L();p.errors=e,a=t}else await R(b);if(xe(p.errors,"root"),M(p.errors)){k.state.next({errors:{}});try{await e(a,r)}catch(e){s=e}}else t&&await t({...p.errors},r),de(),setTimeout(de);if(k.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:M(p.errors)&&!s,submitCount:p.submitCount+1,errors:p.errors}),s)throw s},fe=(e,t={})=>{const r=e?l(e):h,s=l(r),a=M(e),i=a?h:s;if(t.keepDefaultValues||(h=r),!t.keepValues){if(t.keepDirtyValues){const e=new Set([...V.mount,...Object.keys(je(h,x))]);for(const t of Array.from(e))f(p.dirtyFields,t)?_(i,t,f(x,t)):J(t,f(i,t))}else{if(u&&c(e))for(const e of V.mount){const t=f(b,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(re(e)){const t=e.closest("form");if(t){t.reset();break}}}}b={}}x=y.shouldUnregister?t.keepDefaultValues?l(h):{}:l(i),k.array.next({values:{...i}}),k.values.next({values:{...i}})}V={mount:t.keepDirtyValues?V.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},A.mount=!w.isValid||!!t.keepIsValid||!!t.keepDirtyValues,A.watch=!!y.shouldUnregister,k.state.next({submitCount:t.keepSubmitCount?p.submitCount:0,isDirty:!a&&(t.keepDirty?p.isDirty:!(!t.keepDefaultValues||Se(e,h))),isSubmitted:!!t.keepIsSubmitted&&p.isSubmitted,dirtyFields:a?{}:t.keepDirtyValues?t.keepDefaultValues&&x?je(h,x):p.dirtyFields:t.keepDefaultValues&&e?je(h,e):t.keepDirty?p.dirtyFields:{},touchedFields:t.keepTouched?p.touchedFields:{},errors:t.keepErrors?p.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&p.isSubmitSuccessful,isSubmitting:!1})},ye=(e,t)=>fe(te(e)?e(x):e,t);return{control:{register:le,unregister:oe,getFieldState:ie,handleSubmit:ce,setError:ne,_executeSchema:L,_getWatch:$,_getDirty:P,_updateValid:j,_removeUnmounted:()=>{for(const e of V.unMount){const t=f(b,e);t&&(t._f.refs?t._f.refs.every((e=>!ke(e))):!ke(t._f.ref))&&oe(e)}V.unMount=new Set},_updateFieldArray:(e,t=[],r,s,a=!0,i=!0)=>{if(s&&r&&!y.disabled){if(A.action=!0,i&&Array.isArray(f(b,e))){const t=r(f(b,e),s.argA,s.argB);a&&_(b,e,t)}if(i&&Array.isArray(f(p.errors,e))){const t=r(f(p.errors,e),s.argA,s.argB);a&&_(p.errors,e,t),((e,t)=>{!d(f(e,t)).length&&xe(e,t)})(p.errors,e)}if(w.touchedFields&&i&&Array.isArray(f(p.touchedFields,e))){const t=r(f(p.touchedFields,e),s.argA,s.argB);a&&_(p.touchedFields,e,t)}w.dirtyFields&&(p.dirtyFields=je(h,x)),k.state.next({name:e,isDirty:P(e,t),dirtyFields:p.dirtyFields,errors:p.errors,isValid:p.isValid})}else _(x,e,t)},_updateDisabledField:ue,_getFieldArray:e=>d(f(A.mount?x:h,e,y.shouldUnregister?f(h,e,[]):[])),_reset:fe,_resetDefaultValues:()=>te(y.defaultValues)&&y.defaultValues().then((e=>{ye(e,y.resetOptions),k.state.next({isLoading:!1})})),_updateFormState:e=>{p={...p,...e}},_disableForm:e=>{m(e)&&(k.state.next({disabled:e}),Y(b,((t,r)=>{const s=f(b,r);s&&(t.disabled=s._f.disabled||e,Array.isArray(s._f.refs)&&s._f.refs.forEach((t=>{t.disabled=s._f.disabled||e})))}),0,!1))},_subjects:k,_proxyFormState:w,_setErrors:e=>{p.errors=e,k.state.next({errors:p.errors,isValid:!1})},get _fields(){return b},get _formValues(){return x},get _state(){return A},set _state(e){A=e},get _defaultValues(){return h},get _names(){return V},set _names(e){V=e},get _formState(){return p},set _formState(e){p=e},get _options(){return y},set _options(e){y={...y,...e}}},trigger:K,register:le,handleSubmit:ce,watch:(e,t)=>te(e)?k.values.subscribe({next:r=>e($(void 0,t),r)}):$(e,t,!0),setValue:J,getValues:se,reset:ye,resetField:(e,t={})=>{f(b,e)&&(c(t.defaultValue)?J(e,l(f(h,e))):(J(e,t.defaultValue),_(h,e,l(t.defaultValue))),t.keepTouched||xe(p.touchedFields,e),t.keepDirty||(xe(p.dirtyFields,e),p.isDirty=t.defaultValue?P(e,l(f(h,e))):P()),t.keepError||(xe(p.errors,e),w.isValid&&j()),k.state.next({...p}))},clearErrors:e=>{e&&B(e).forEach((e=>xe(p.errors,e))),k.state.next({errors:e?p.errors:{}})},unregister:oe,setError:ne,setFocus:(e,t={})=>{const r=f(b,e),s=r&&r._f;if(s){const e=s.refs?s.refs[0]:s.ref;e.focus&&(e.focus(),t.shouldSelect&&te(e.select)&&e.select())}},getFieldState:ie}}exports.Controller=e=>e.render(I(e)),exports.Form=function(t){const r=T(),[s,a]=e.useState(!1),{control:i=r.control,onSubmit:n,children:o,action:u,method:l=J,headers:d,encType:c,onError:f,render:m,onSuccess:y,validateStatus:p,..._}=t,g=async e=>{let r=!1,s="";await i.handleSubmit((async t=>{const a=new FormData;let o="";try{o=JSON.stringify(t)}catch(e){}const m=H(i._formValues);for(const e in m)a.append(e,m[e]);if(n&&await n({data:t,event:e,method:l,formData:a,formDataJson:o}),u)try{const e=[d&&d["Content-Type"],c].some((e=>e&&e.includes("json"))),t=await fetch(String(u),{method:l,headers:{...d,...c?{"Content-Type":c}:{}},body:e?o:a});t&&(p?!p(t.status):t.status<200||t.status>=300)?(r=!0,f&&f({response:t}),s=String(t.status)):y&&y({response:t})}catch(e){r=!0,f&&f({error:e})}}))(e),r&&t.control&&(t.control._subjects.state.next({isSubmitSuccessful:!1}),t.control.setError("root.server",{type:s}))};return e.useEffect((()=>{a(!0)}),[]),m?e.createElement(e.Fragment,null,m({submit:g})):e.createElement("form",{noValidate:s,action:u,method:l,encType:c,onSubmit:g,..._},o)},exports.FormProvider=t=>{const{children:r,...s}=t;return e.createElement(O.Provider,{value:s},r)},exports.appendErrors=z,exports.get=f,exports.set=_,exports.useController=I,exports.useFieldArray=function(t){const r=T(),{control:s=r.control,name:a,keyName:i="id",shouldUnregister:n,rules:o}=t,[u,d]=e.useState(s._getFieldArray(a)),c=e.useRef(s._getFieldArray(a).map(G)),m=e.useRef(u),y=e.useRef(a),p=e.useRef(!1);y.current=a,m.current=u,s._names.array.add(a),o&&s.register(a,o),R({next:({values:e,name:t})=>{if(t===y.current||!t){const t=f(e,y.current);Array.isArray(t)&&(d(t),c.current=t.map(G))}},subject:s._subjects.array});const g=e.useCallback((e=>{p.current=!0,s._updateFieldArray(a,e)}),[s,a]);return e.useEffect((()=>{if(s._state.action=!1,X(a,s._names)&&s._subjects.state.next({...s._formState}),p.current&&(!Q(s._options.mode).isOnSubmit||s._formState.isSubmitted))if(s._options.resolver)s._executeSchema([a]).then((e=>{const t=f(e.errors,a),r=f(s._formState.errors,a);(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type)&&(t?_(s._formState.errors,a,t):xe(s._formState.errors,a),s._subjects.state.next({errors:s._formState.errors}))}));else{const e=f(s._fields,a);!e||!e._f||Q(s._options.reValidateMode).isOnSubmit&&Q(s._options.mode).isOnSubmit||me(e,s._names.disabled,s._formValues,s._options.criteriaMode===F,s._options.shouldUseNativeValidation,!0).then((e=>!M(e)&&s._subjects.state.next({errors:Z(s._formState.errors,e,a)})))}s._subjects.values.next({name:a,values:{...s._formValues}}),s._names.focus&&Y(s._fields,((e,t)=>{if(s._names.focus&&t.startsWith(s._names.focus)&&e.focus)return e.focus(),1})),s._names.focus="",s._updateValid(),p.current=!1}),[u,a,s]),e.useEffect((()=>(!f(s._formValues,a)&&s._updateFieldArray(a),()=>{(s._options.shouldUnregister||n)&&s.unregister(a)})),[a,s,i,n]),{swap:e.useCallback(((e,t)=>{const r=s._getFieldArray(a);he(r,e,t),he(c.current,e,t),g(r),d(r),s._updateFieldArray(a,r,he,{argA:e,argB:t},!1)}),[g,a,s]),move:e.useCallback(((e,t)=>{const r=s._getFieldArray(a);ge(r,e,t),ge(c.current,e,t),g(r),d(r),s._updateFieldArray(a,r,ge,{argA:e,argB:t},!1)}),[g,a,s]),prepend:e.useCallback(((e,t)=>{const r=B(l(e)),i=ve(s._getFieldArray(a),r);s._names.focus=K(a,0,t),c.current=ve(c.current,r.map(G)),g(i),d(i),s._updateFieldArray(a,i,ve,{argA:pe(e)})}),[g,a,s]),append:e.useCallback(((e,t)=>{const r=B(l(e)),i=ye(s._getFieldArray(a),r);s._names.focus=K(a,i.length-1,t),c.current=ye(c.current,r.map(G)),g(i),d(i),s._updateFieldArray(a,i,ye,{argA:pe(e)})}),[g,a,s]),remove:e.useCallback((e=>{const t=be(s._getFieldArray(a),e);c.current=be(c.current,e),g(t),d(t),!Array.isArray(f(s._fields,a))&&_(s._fields,a,void 0),s._updateFieldArray(a,t,be,{argA:e})}),[g,a,s]),insert:e.useCallback(((e,t,r)=>{const i=B(l(t)),n=_e(s._getFieldArray(a),e,i);s._names.focus=K(a,e,r),c.current=_e(c.current,e,i.map(G)),g(n),d(n),s._updateFieldArray(a,n,_e,{argA:e,argB:pe(t)})}),[g,a,s]),update:e.useCallback(((e,t)=>{const r=l(t),i=Ae(s._getFieldArray(a),e,r);c.current=[...i].map(((t,r)=>t&&r!==e?c.current[r]:G())),g(i),d([...i]),s._updateFieldArray(a,i,Ae,{argA:e,argB:r},!0,!1)}),[g,a,s]),replace:e.useCallback((e=>{const t=B(l(e));c.current=t.map(G),g([...t]),d([...t]),s._updateFieldArray(a,[...t],(e=>e),{},!0,!1)}),[g,a,s]),fields:e.useMemo((()=>u.map(((e,t)=>({...e,[i]:c.current[t]||G()})))),[u,i])}},exports.useForm=function(t={}){const r=e.useRef(void 0),s=e.useRef(void 0),[a,i]=e.useState({isDirty:!1,isValidating:!1,isLoading:te(t.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||!1,defaultValues:te(t.defaultValues)?void 0:t.defaultValues});r.current||(r.current={...Le(t),formState:a});const n=r.current.control;return n._options=t,R({subject:n._subjects.state,next:e=>{U(e,n._proxyFormState,n._updateFormState,!0)&&i({...n._formState})}}),e.useEffect((()=>n._disableForm(t.disabled)),[n,t.disabled]),e.useEffect((()=>{if(n._proxyFormState.isDirty){const e=n._getDirty();e!==a.isDirty&&n._subjects.state.next({isDirty:e})}}),[n,a.isDirty]),e.useEffect((()=>{t.values&&!Se(t.values,s.current)?(n._reset(t.values,n._options.resetOptions),s.current=t.values,i((e=>({...e})))):n._resetDefaultValues()}),[t.values,n]),e.useEffect((()=>{t.errors&&n._setErrors(t.errors)}),[t.errors,n]),e.useEffect((()=>{n._state.mount||(n._updateValid(),n._state.mount=!0),n._state.watch&&(n._state.watch=!1,n._subjects.state.next({...n._formState})),n._removeUnmounted()})),e.useEffect((()=>{t.shouldUnregister&&n._subjects.values.next({values:n._getWatch()})}),[t.shouldUnregister,n]),r.current.formState=N(a,n),r.current},exports.useFormContext=T,exports.useFormState=P,exports.useWatch=$;
//# sourceMappingURL=index.cjs.js.map
